#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 3: Docker Stack Deployment
===================================================

This script handles the Docker stack deployment for a new tenant in the LEOS360 platform.
It deploys or updates Docker Compose stacks using the Portainer API, managing the complete
containerized infrastructure for the customer.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 and Stage 2 must be completed successfully
- Portainer API must be accessible
- Docker Compose file must exist in tenant directory
- Portainer API credentials must be configured

Usage:
    python3 stage3_tenant_docker.py <customer_name> [options]

Examples:
    python3 stage3_tenant_docker.py example-customer
    python3 stage3_tenant_docker.py example-customer --delete
    python3 stage3_tenant_docker.py example-customer --status
    python3 stage3_tenant_docker.py example-customer --skip-keycloak-check
"""

import os
import sys
import json
import time
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Any
import requests
import urllib3
from dotenv import load_dotenv

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 3: Docker Stack Deployment"

# File paths
DOCKER_CONFIG_PATH = Path("/mnt/storage/docker/.env")
BASE_TENANT_PATH = Path("/mnt/storage/tenants")

# Portainer configuration
DEFAULT_ENDPOINT_ID = "11"
PORTAINER_PORT = 9443
API_TIMEOUT = 30

# Keycloak monitoring configuration
KEYCLOAK_STARTUP_TIMEOUT = 300  # 5 minutes timeout for Keycloak startup
KEYCLOAK_LOG_CHECK_INTERVAL = 5  # Check logs every 5 seconds
KEYCLOAK_STARTUP_MESSAGE = "Keycloak"  # Look for this in logs
KEYCLOAK_READY_MESSAGE = "started in"  # Additional confirmation message

# Disable SSL warnings for Portainer API
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


def print_debug(message: str) -> None:
    """Print a formatted debug message."""
    print(f"[DEBUG] {message}")


# =============================================================================
# MAIN DOCKER STACK DEPLOYMENT CLASS
# =============================================================================

class TenantDockerStackSetup:
    """
    Handles Docker stack deployment for a new tenant in the LEOS360 platform.

    This class manages the complete Docker stack deployment process including:
    - Portainer API configuration and authentication
    - Docker Compose file validation and parsing
    - Environment variable management
    - Stack creation, updating, and deletion
    - Error handling and status reporting
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant Docker stack setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValueError: If customer_name is invalid
            FileNotFoundError: If required files are missing
        """
        self.customer_name = customer_name
        print_step(f"Initializing Docker stack setup for: {customer_name}")

        # Stack configuration
        self.stack_name = f"leos360-{customer_name}"

        # File paths
        self.customer_dir = BASE_TENANT_PATH / customer_name
        self.env_file = self.customer_dir / ".env"

        # Find docker-compose file
        self.compose_file = self._find_compose_file()

        # Portainer configuration (will be loaded later)
        self.portainer_config = {}

        # Configuration options
        self.skip_keycloak_check = False

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def _find_compose_file(self) -> Path:
        """
        Find the Docker Compose file in the customer directory.

        Returns:
            Path to the Docker Compose file

        Raises:
            FileNotFoundError: If no compose file is found
        """
        # Check for docker-compose.yaml first, then .yml
        compose_yaml = self.customer_dir / "docker-compose.yaml"
        compose_yml = self.customer_dir / "docker-compose.yml"

        if compose_yaml.exists():
            return compose_yaml
        elif compose_yml.exists():
            return compose_yml
        else:
            raise FileNotFoundError(f"No docker-compose.y(a)ml found in {self.customer_dir}")

    def load_portainer_config(self) -> None:
        """
        Load Portainer configuration from environment file.

        Raises:
            FileNotFoundError: If config file doesn't exist
            ValueError: If required configuration is missing
        """
        if not DOCKER_CONFIG_PATH.exists():
            raise FileNotFoundError(f"Docker config file not found: {DOCKER_CONFIG_PATH}")

        load_dotenv(DOCKER_CONFIG_PATH)

        api_key = os.getenv("PORTAINER_API_KEY")
        host = os.getenv("PORTAINER_HOST")

        if not api_key or not host:
            raise ValueError("PORTAINER_API_KEY or PORTAINER_HOST not defined in config")

        self.portainer_config = {
            "api_key": api_key,
            "host": host,
            "endpoint_id": os.getenv("ENDPOINT_ID", DEFAULT_ENDPOINT_ID),
            "portainer_url": f"https://{host}:{PORTAINER_PORT}"
        }

        print_step("Portainer configuration loaded successfully")

    def parse_env_file(self) -> List[Dict[str, str]]:
        """
        Parse environment variables from customer .env file.

        Returns:
            List of environment variable dictionaries
        """
        env_vars = []

        if not self.env_file.exists():
            print_warning("No .env file found for customer")
            return env_vars

        try:
            with open(self.env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    if '=' in line:
                        key, value = line.split('=', 1)
                        env_vars.append({"name": key.strip(), "value": value.strip()})

            print_step(f"Found {len(env_vars)} environment variables")
            return env_vars

        except Exception as e:
            print_error(f"Error reading environment file: {e}")
            return []

    # =========================================================================
    # PORTAINER API METHODS
    # =========================================================================

    def make_api_request(self, method: str, url: str, data: Optional[Dict] = None,
                        files: Optional[Dict] = None, use_json: bool = True) -> requests.Response:
        """
        Make an API request to Portainer.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            url: API endpoint URL
            data: Optional request data
            files: Optional files for multipart upload
            use_json: Whether to use JSON headers

        Returns:
            Response object

        Raises:
            Exception: If API request fails
        """
        if use_json:
            headers = {
                "X-API-Key": self.portainer_config["api_key"],
                "Content-Type": "application/json"
            }
        else:
            headers = {"X-API-Key": self.portainer_config["api_key"]}

        try:
            print_debug(f"Making {method} request to {url}")

            if method.upper() == "GET":
                response = requests.get(url, headers=headers, verify=False, timeout=API_TIMEOUT)
            elif method.upper() == "POST":
                if files:
                    response = requests.post(url, headers=headers, files=files, data=data,
                                           verify=False, timeout=API_TIMEOUT)
                else:
                    response = requests.post(url, headers=headers, json=data,
                                           verify=False, timeout=API_TIMEOUT)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=headers, json=data,
                                      verify=False, timeout=API_TIMEOUT)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=headers, verify=False, timeout=API_TIMEOUT)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            print_debug(f"Response status: {response.status_code}")
            return response

        except requests.exceptions.RequestException as e:
            print_error(f"API request failed: {e}")
            raise

    def get_existing_stacks(self) -> List[Dict[str, Any]]:
        """
        Get list of existing stacks from Portainer.

        Returns:
            List of stack dictionaries

        Raises:
            Exception: If API request fails
        """
        url = f"{self.portainer_config['portainer_url']}/api/stacks"
        response = self.make_api_request("GET", url)

        if response.status_code != 200:
            raise Exception(f"Failed to get stacks: {response.status_code} - {response.text}")

        return response.json()

    def find_stack_by_name(self, stack_name: str) -> Optional[Dict[str, Any]]:
        """
        Find a stack by name.

        Args:
            stack_name: Name of the stack to find

        Returns:
            Stack dictionary if found, None otherwise
        """
        stacks = self.get_existing_stacks()

        for stack in stacks:
            if stack.get("Name") == stack_name:
                return stack

        return None

    # =========================================================================
    # CONTAINER MONITORING METHODS
    # =========================================================================

    def find_container_by_name(self, container_name: str) -> Optional[str]:
        """
        Find a container by name and return its ID.

        Args:
            container_name: Name of the container to find

        Returns:
            Container ID if found, None otherwise
        """
        endpoint_id = self.portainer_config["endpoint_id"]
        url = f"{self.portainer_config['portainer_url']}/api/endpoints/{endpoint_id}/docker/containers/json"

        try:
            response = self.make_api_request("GET", url)

            if response.status_code != 200:
                print_error(f"Failed to get containers: {response.status_code} - {response.text}")
                return None

            containers = response.json()

            for container in containers:
                names = container.get("Names", [])
                for name in names:
                    # Remove leading slash from container name
                    clean_name = name.lstrip("/")
                    if clean_name == container_name:
                        return container.get("Id")

            return None

        except Exception as e:
            print_error(f"Exception while finding container: {e}")
            return None

    def get_container_logs(self, container_id: str, tail_lines: int = 100) -> Optional[str]:
        """
        Get logs from a container.

        Args:
            container_id: ID of the container
            tail_lines: Number of recent lines to retrieve

        Returns:
            Container logs as string, None if failed
        """
        endpoint_id = self.portainer_config["endpoint_id"]
        url = f"{self.portainer_config['portainer_url']}/api/endpoints/{endpoint_id}/docker/containers/{container_id}/logs"

        params = {
            "stdout": "true",
            "stderr": "true",
            "tail": str(tail_lines),
            "timestamps": "true"
        }

        try:
            response = self.make_api_request("GET", url + "?" + "&".join([f"{k}={v}" for k, v in params.items()]))

            if response.status_code != 200:
                print_error(f"Failed to get container logs: {response.status_code} - {response.text}")
                return None

            return response.text

        except Exception as e:
            print_error(f"Exception while getting container logs: {e}")
            return None

    def wait_for_keycloak_ready(self) -> bool:
        """
        Wait for Keycloak container to be fully started and ready.

        Returns:
            True if Keycloak is ready, False if timeout or error
        """
        keycloak_container_name = f"{self.customer_name}-keycloak"
        print_step(f"Waiting for Keycloak container '{keycloak_container_name}' to be ready...")

        start_time = time.time()
        timeout = KEYCLOAK_STARTUP_TIMEOUT

        while time.time() - start_time < timeout:
            try:
                # Find Keycloak container
                container_id = self.find_container_by_name(keycloak_container_name)

                if not container_id:
                    print_debug(f"Keycloak container not found yet, waiting...")
                    time.sleep(KEYCLOAK_LOG_CHECK_INTERVAL)
                    continue

                # Get container logs
                logs = self.get_container_logs(container_id, tail_lines=50)

                if logs:
                    # Check for the specific startup message
                    if self._check_keycloak_startup_message(logs):
                        elapsed_time = time.time() - start_time
                        print_success(f"Keycloak container is ready! (took {elapsed_time:.1f} seconds)")
                        return True

                # Wait before next check
                remaining_time = timeout - (time.time() - start_time)
                print_debug(f"Keycloak not ready yet, checking again in {KEYCLOAK_LOG_CHECK_INTERVAL}s (timeout in {remaining_time:.1f}s)")
                time.sleep(KEYCLOAK_LOG_CHECK_INTERVAL)

            except Exception as e:
                print_warning(f"Error checking Keycloak status: {e}")
                time.sleep(KEYCLOAK_LOG_CHECK_INTERVAL)

        # Timeout reached
        elapsed_time = time.time() - start_time
        print_error(f"Timeout waiting for Keycloak to be ready after {elapsed_time:.1f} seconds")
        return False

    def _check_keycloak_startup_message(self, logs: str) -> bool:
        """
        Check if Keycloak startup message is present in logs.

        Args:
            logs: Container logs to check

        Returns:
            True if startup message found, False otherwise
        """
        # Look for the specific Keycloak startup message
        target_messages = [
            "Keycloak",
            "started in",
            "Listening on: http://0.0.0.0:8080",
            "Profile prod activated"
        ]

        # Check if all required messages are present
        found_messages = []
        for message in target_messages:
            if message in logs:
                found_messages.append(message)

        # We need at least Keycloak name and "started in" message
        if "Keycloak" in found_messages and "started in" in found_messages:
            print_debug(f"Found Keycloak startup indicators: {found_messages}")
            return True

        print_debug(f"Keycloak startup messages not yet complete. Found: {found_messages}")
        return False

    def check_keycloak_startup(self) -> bool:
        """
        Main method to check if Keycloak container has started successfully.

        Returns:
            True if Keycloak is ready, False otherwise
        """
        print_header("KEYCLOAK STARTUP VERIFICATION")
        print_step("Verifying Keycloak container startup...")

        try:
            return self.wait_for_keycloak_ready()

        except Exception as e:
            print_error(f"Failed to verify Keycloak startup: {e}")
            return False

    # =========================================================================
    # STACK OPERATION METHODS
    # =========================================================================

    def create_stack(self, env_vars: List[Dict[str, str]]) -> bool:
        """
        Create a new Docker stack.

        Args:
            env_vars: List of environment variables

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Creating new stack: {self.stack_name}")

        # Prepare multipart form data
        with open(self.compose_file, 'rb') as f:
            files = {'file': (self.compose_file.name, f)}
            data = {
                'Name': self.stack_name,
                'Env': json.dumps(env_vars)
            }

            url = f"{self.portainer_config['portainer_url']}/api/stacks/create/standalone/file?endpointId={self.portainer_config['endpoint_id']}"

            try:
                response = self.make_api_request("POST", url, data=data, files=files, use_json=False)

                if 200 <= response.status_code < 300:
                    print_success(f"Stack {self.stack_name} created successfully!")
                    return True
                else:
                    print_error(f"Failed to create stack: {response.status_code} - {response.text}")
                    return False

            except Exception as e:
                print_error(f"Exception during stack creation: {e}")
                return False

    def update_stack(self, stack_id: str, env_vars: List[Dict[str, str]]) -> bool:
        """
        Update an existing Docker stack.

        Args:
            stack_id: ID of the stack to update
            env_vars: List of environment variables

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Updating existing stack: {self.stack_name} (ID: {stack_id})")

        # Read compose file content
        with open(self.compose_file, 'r') as f:
            compose_content = f.read()

        # Prepare update payload
        update_payload = {
            "stackFileContent": compose_content,
            "env": env_vars,
            "prune": True  # Remove unused containers
        }

        url = f"{self.portainer_config['portainer_url']}/api/stacks/{stack_id}?endpointId={self.portainer_config['endpoint_id']}"

        try:
            response = self.make_api_request("PUT", url, data=update_payload)

            if 200 <= response.status_code < 300:
                print_success(f"Stack {self.stack_name} updated successfully!")
                return True
            else:
                print_error(f"Failed to update stack: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print_error(f"Exception during stack update: {e}")
            return False

    def delete_stack(self, stack_id: str) -> bool:
        """
        Delete a Docker stack.

        Args:
            stack_id: ID of the stack to delete

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Deleting stack: {self.stack_name} (ID: {stack_id})")

        url = f"{self.portainer_config['portainer_url']}/api/stacks/{stack_id}"

        try:
            response = self.make_api_request("DELETE", url)

            if response.status_code in [200, 204, 404]:
                print_success(f"Stack {self.stack_name} deleted successfully!")
                return True
            else:
                print_error(f"Failed to delete stack: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print_error(f"Exception during stack deletion: {e}")
            return False

    def show_stack_status(self) -> None:
        """
        Show status of the Docker stack for this customer.
        """
        print_step(f"Checking stack status for customer: {self.customer_name}")

        try:
            existing_stack = self.find_stack_by_name(self.stack_name)

            if existing_stack:
                print_success(f"Stack {self.stack_name} exists:")
                print(f"  - ID: {existing_stack.get('Id')}")
                print(f"  - Status: {existing_stack.get('Status', 'Unknown')}")
                print(f"  - Endpoint ID: {existing_stack.get('EndpointId')}")
                print(f"  - Created: {existing_stack.get('CreationDate', 'Unknown')}")
            else:
                print_warning(f"Stack {self.stack_name} does not exist")

        except Exception as e:
            print_error(f"Failed to check stack status: {e}")

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str) -> bool:
        """
        Main execution function that orchestrates the Docker stack deployment process.

        Args:
            operation: Operation to perform ('deploy', 'delete', or 'status')

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 Docker Stack Deployment - {self.customer_name}")
        print_step(f"Starting Docker stack {operation} for {self.customer_name}...")

        try:
            # Step 1: Validate customer directory
            if not self.customer_dir.exists():
                raise FileNotFoundError(f"Customer directory does not exist: {self.customer_dir}")

            # Step 2: Load Portainer configuration
            self.load_portainer_config()

            # Step 3: Parse environment variables
            env_vars = self.parse_env_file()

            # Step 4: Perform requested operation
            if operation == "status":
                self.show_stack_status()
                return True
            elif operation == "delete":
                existing_stack = self.find_stack_by_name(self.stack_name)
                if existing_stack:
                    success = self.delete_stack(existing_stack["Id"])
                    if success:
                        print_success(f"Stack {self.stack_name} deleted successfully!")
                    return success
                else:
                    print_warning(f"Stack {self.stack_name} does not exist, nothing to delete")
                    return True
            else:  # default: deploy
                existing_stack = self.find_stack_by_name(self.stack_name)

                if existing_stack:
                    # Update existing stack
                    success = self.update_stack(existing_stack["Id"], env_vars)
                    if not success:
                        # If update fails, try delete and recreate
                        print_warning("Update failed, attempting delete and recreate...")
                        if self.delete_stack(existing_stack["Id"]):
                            time.sleep(3)  # Wait for deletion to complete
                            success = self.create_stack(env_vars)
                else:
                    # Create new stack
                    success = self.create_stack(env_vars)

                if success:
                    print_header("DOCKER STACK DEPLOYED SUCCESSFULLY")
                    print_success(f"Docker stack deployment for {self.customer_name} completed successfully!")

                    # Check if Keycloak verification should be skipped
                    if self.skip_keycloak_check:
                        print_warning("Skipping Keycloak startup verification (--skip-keycloak-check flag used)")
                        print_header("OPERATION COMPLETED SUCCESSFULLY")
                        print_success(f"Docker stack deployment completed successfully!")
                    else:
                        # Wait for Keycloak to be ready before proceeding
                        print_step("Waiting for Keycloak container to be fully ready...")
                        keycloak_ready = self.check_keycloak_startup()

                        if keycloak_ready:
                            print_header("OPERATION COMPLETED SUCCESSFULLY")
                            print_success(f"Docker stack deployment and Keycloak startup verification completed successfully!")
                            print_success("Ready to proceed with Stage 4 (Service Configuration)")
                        else:
                            print_warning("Docker stack deployed but Keycloak startup verification failed")
                            print_warning("You may need to check Keycloak container logs manually before proceeding to Stage 4")
                            # Still return success for stack deployment, but with warning
                            success = True

                return success

        except Exception as e:
            print_error(f"Docker stack {operation} failed: {str(e)}")
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute Docker stack deployment.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Deploy Docker stack for LEOS360 platform tenant',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage3_tenant_docker.py example-customer
  python3 stage3_tenant_docker.py example-customer --delete
  python3 stage3_tenant_docker.py example-customer --status
  python3 stage3_tenant_docker.py example-customer --skip-keycloak-check

Requirements:
  - Stage 1 and Stage 2 must be completed successfully
  - Portainer API must be accessible
  - Docker Compose file must exist in tenant directory

Notes:
  - By default, the script waits for Keycloak container to be fully started
  - Use --skip-keycloak-check to bypass Keycloak startup verification
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    parser.add_argument(
        '--delete',
        action='store_true',
        help='Delete the Docker stack for the customer'
    )
    parser.add_argument(
        '--status',
        action='store_true',
        help='Show status of the Docker stack'
    )
    parser.add_argument(
        '--skip-keycloak-check',
        action='store_true',
        help='Skip Keycloak startup verification after deployment'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    if args.status:
        operation = "status"
    elif args.delete:
        operation = "delete"
    else:
        operation = "deploy"

    try:
        # Create and run Docker stack setup
        setup = TenantDockerStackSetup(args.customer_name)
        setup.skip_keycloak_check = args.skip_keycloak_check
        success = setup.run(operation)

        if not success:
            sys.exit(1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except FileNotFoundError as e:
        print_error(f"File not found: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Docker stack deployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()