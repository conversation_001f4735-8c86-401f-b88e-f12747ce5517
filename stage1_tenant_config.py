#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 1: Tenant Configuration Setup
======================================================

This script handles the initial configuration setup for a new tenant in the LEOS360 platform.
It creates the directory structure, generates secure passwords, and sets up configuration files
for all required services.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Must be run as root
- Master configuration templates must exist in /mnt/storage/setup
- SSL certificates must be available
- Target tenant directory must not already exist

Usage:
    python3 stage1_tenant_config.py <customer_name>

Example:
    python3 stage1_tenant_config.py example-customer
"""

import os
import sys
import re
import shutil
import argparse
import secrets
import base64
import datetime
from pathlib import Path
from typing import Dict, Set, Optional, Tuple

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 1: Tenant Configuration Setup"

# Base paths and configuration
BASE_DOMAIN = "leos360.cloud"
MASTER_BASE_PATH = Path("/mnt/storage/setup")
TENANTS_BASE_PATH = Path("/mnt/storage/tenants")

# Network configuration
EXTERNAL_DB_HOST = "************"
EXTERNAL_DB_PORT = "5432"
IP_PREFIX = "172.16.7."
IP_START = 20
IP_END = 150

# File permissions
DIR_PERMISSIONS = 0o755
FILE_PERMISSIONS = 0o644
SCRIPT_PERMISSIONS = 0o755
SECRET_DIR_PERMISSIONS = 0o750
SECRET_FILE_PERMISSIONS = 0o640


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


# =============================================================================
# MAIN TENANT CONFIGURATION CLASS
# =============================================================================

class TenantConfigSetup:
    """
    Handles configuration setup for a new tenant in the LEOS360 platform.

    This class manages the complete setup process including:
    - Directory structure creation
    - Password generation and storage
    - Service configuration files
    - SSL certificate setup
    - Environment file creation
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant configuration.

        Args:
            customer_name: Name of the customer/tenant (lowercase alphanumeric with hyphens)

        Raises:
            ValueError: If customer_name contains invalid characters
        """
        # Validate customer name format
        if not re.match(r'^[a-z0-9-]+$', customer_name):
            raise ValueError("Customer name must contain only lowercase letters, numbers and hyphens")

        self.customer_name = customer_name
        print_step(f"Initializing tenant configuration for: {customer_name}")

        # Base configuration
        self.base_domain = BASE_DOMAIN
        self.master_base = MASTER_BASE_PATH
        self.ssl_base = self.master_base / "ssl"
        self.keycloak_realm = f"{customer_name}.{self.base_domain}"

        # Customer specific configuration
        self.customer_domain = f"{customer_name}.{self.base_domain}"
        self.config_base = TENANTS_BASE_PATH / customer_name

        # Database configuration
        self.external_db_host = EXTERNAL_DB_HOST
        self.external_db_port = EXTERNAL_DB_PORT

        # Service configuration paths
        self.dovecot_config = self.config_base / "dovecot"
        self.lldap_config = self.config_base / "lldap"
        self.keycloak_config = self.config_base / "keycloak"
        self.postfix_config = self.config_base / "postfix"
        self.db_config = self.config_base / "db"
        self.redis_config = self.config_base / "redis"
        self.nextcloud_config = self.config_base / "nextcloud"

        # Runtime variables (will be populated during setup)
        self.customer_ip = ""
        self.redis_db_index = ""
        self.redis_secret = ""
        self.signal_secret = ""

        # Generated passwords (will be populated during setup)
        self.db_password = ""
        self.nextcloud_admin_password = ""
        self.keycloak_admin_password = ""
        self.lldap_jwt_secret = ""
        self.lldap_key_seed = ""
        self.lldap_admin_pass = ""
        self.lldap_ro_pass = ""
        self.nextcloud_keycloak_client_secret = ""
        self.leos360portal_keycloak_client_secret = ""

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def replace_variables(self, content: str, replacements: Dict[str, str]) -> str:
        """
        Replace variables in content with their values.

        Args:
            content: String containing variables to replace
            replacements: Dictionary of placeholder-value pairs

        Returns:
            Content with all placeholders replaced
        """
        for placeholder, value in replacements.items():
            if placeholder.startswith("${") and placeholder.endswith("}"):
                content = content.replace(placeholder, value)
        return content

    def copy_and_set_permissions(self, source: Path, target: Path,
                               permissions: int = FILE_PERMISSIONS) -> bool:
        """
        Copy file and set permissions.

        Args:
            source: Source file path
            target: Destination file path
            permissions: File permissions (default: FILE_PERMISSIONS)

        Returns:
            True if successful, False otherwise
        """
        if not source.exists():
            print_warning(f"Source file {source} not found")
            return False

        target.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy(source, target)
        os.chmod(target, permissions)
        print_step(f"Copied {source.name} to {target}")
        return True

    def ensure_directory(self, directory: Path, permissions: int = DIR_PERMISSIONS) -> Path:
        """
        Ensure directory exists with proper permissions.

        Args:
            directory: Directory path
            permissions: Directory permissions (default: DIR_PERMISSIONS)

        Returns:
            Path to the created directory
        """
        directory.mkdir(parents=True, exist_ok=True)
        os.chmod(directory, permissions)
        return directory

    def generate_secure_password(self, length: int = 32) -> str:
        """
        Generate a secure random password.

        Args:
            length: Length of the password to generate

        Returns:
            Secure random password string
        """
        # Generate random bytes
        random_bytes = secrets.token_bytes(length)
        # Convert to base64 and remove potentially problematic characters
        password = base64.b64encode(random_bytes).decode('utf-8')
        # Trim to specified length and remove problematic characters
        password = re.sub(r'[=+/]', '', password)[:length]
        return password

    # =========================================================================
    # IP ALLOCATION METHODS
    # =========================================================================

    def _find_tenant_dirs(self, search_path: str) -> Set[str]:
        """
        Find all tenant directories.

        Args:
            search_path: Path to search for tenant directories

        Returns:
            Set of tenant directory paths
        """
        try:
            return {d for d in os.listdir(search_path)
                   if os.path.isdir(os.path.join(search_path, d))}
        except Exception as e:
            print_error(f"Error listing directories: {e}")
            return set()

    def _get_used_ips_and_indices(self, tenant_dirs: Set[str]) -> Tuple[Set[str], Set[int]]:
        """
        Get used IPs and indices from tenant directories.

        Args:
            tenant_dirs: Set of tenant directory paths

        Returns:
            Tuple of (used_ips, used_indices)
        """
        used_ips = set()
        used_indices = set()

        for tenant_dir in tenant_dirs:
            env_file = TENANTS_BASE_PATH / tenant_dir / ".env"
            if env_file.exists():
                try:
                    content = env_file.read_text()
                    if "CUSTOMER_IP=" in content:
                        ip = content.split("CUSTOMER_IP=")[1].split("\n")[0].strip()
                        if ip:
                            used_ips.add(ip)
                            try:
                                used_indices.add(int(ip.split('.')[-1]))
                            except ValueError:
                                pass

                    if "REDIS_DB_INDEX=" in content:
                        db_index = content.split("REDIS_DB_INDEX=")[1].split("\n")[0].strip()
                        if db_index:
                            try:
                                used_indices.add(int(db_index))
                            except ValueError:
                                pass
                except Exception as e:
                    print_warning(f"Could not process {env_file}: {e}")

        return used_ips, used_indices

    def setup_nextfreeip(self) -> None:
        """
        Get the next free IP address and Redis DB index for the customer.

        Raises:
            SystemExit: If no available IP addresses or Redis DB indices found
        """
        print_step("Finding next available IP address and Redis DB index...")

        # Find used resources
        tenant_dirs = self._find_tenant_dirs(str(TENANTS_BASE_PATH))
        _, used_indices = self._get_used_ips_and_indices(tenant_dirs)

        # Find next available
        for i in range(IP_START, IP_END + 1):
            if i not in used_indices:
                self.customer_ip = f"{IP_PREFIX}{i}"
                self.redis_db_index = str(i)
                print_success(f"Allocated IP: {self.customer_ip}, Redis DB Index: {self.redis_db_index}")
                return

        print_error("No available IP addresses or Redis DB indices found")
        sys.exit(1)

    # =========================================================================
    # PREREQUISITE VALIDATION METHODS
    # =========================================================================

    def check_prerequisites(self) -> None:
        """
        Validate prerequisites and requirements.

        Raises:
            SystemExit: If any prerequisite check fails
        """
        print_step("Checking prerequisites...")

        # Check if running as root
        if os.geteuid() != 0:
            print_error("This script must be run as root")
            sys.exit(1)

        # Check required directories
        if not self.master_base.exists():
            print_error(f"Master directory {self.master_base} does not exist")
            sys.exit(1)

        if not self.ssl_base.exists():
            print_error(f"SSL directory {self.ssl_base} does not exist")
            sys.exit(1)

        # Check if customer directory already exists
        if self.config_base.exists():
            print_error(f"Customer directory {self.config_base} already exists")
            sys.exit(1)

        # Check required template files
        required_files = [
            self.master_base / ".env",
            self.master_base / "db" / "db_setup.sql.template",
            self.master_base / "dovecot" / "dovecot-ldap-userdb.conf",
            self.master_base / "dovecot" / "dovecot-ldap-passdb.conf",
            self.master_base / "postfix" / "ldap" / "virtual_aliases.cf",
            self.master_base / "postfix" / "ldap" / "virtual_domains.cf",
        ]

        missing_files = []
        for file in required_files:
            if not file.exists():
                missing_files.append(str(file))

        if missing_files:
            print_error("Required template files not found:")
            for file in missing_files:
                print_error(f"  - {file}")
            sys.exit(1)

        print_success("All prerequisites validated successfully")

    # =========================================================================
    # PASSWORD GENERATION METHODS
    # =========================================================================

    def generate_passwords(self) -> None:
        """
        Generate secure passwords and save to credentials file.
        """
        print_step("Generating secure passwords...")

        # Generate all required passwords
        self.db_password = self.generate_secure_password()
        self.nextcloud_admin_password = self.generate_secure_password()
        self.keycloak_admin_password = self.generate_secure_password()
        self.lldap_jwt_secret = self.generate_secure_password()
        self.lldap_key_seed = self.generate_secure_password()
        self.lldap_admin_pass = self.generate_secure_password()
        self.lldap_ro_pass = self.generate_secure_password()
        self.signal_secret = self.generate_secure_password()
        self.redis_secret = self.generate_secure_password()
        self.nextcloud_keycloak_client_secret = self.generate_secure_password()
        self.leos360portal_keycloak_client_secret = self.generate_secure_password()

        # Create .secrets directory
        secrets_dir = self.config_base / ".secrets"
        self.ensure_directory(secrets_dir, SECRET_DIR_PERMISSIONS)

        # Save passwords to credentials file
        credentials_content = f"""# Generated credentials for {self.customer_name}
# Generated on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Customer IP: {self.customer_ip}
Database Password: {self.db_password}
Nextcloud Admin Password: {self.nextcloud_admin_password}
Keycloak Admin Password: {self.keycloak_admin_password}
LLDAP JWT Secret: {self.lldap_jwt_secret}
LLDAP Key Seed: {self.lldap_key_seed}
LLDAP Admin Password: {self.lldap_admin_pass}
LLDAP RO Password: {self.lldap_ro_pass}
Signal Secret: {self.signal_secret}
Redis DB Index: {self.redis_db_index}
Redis Password: {self.redis_secret}
Nextcloud Keycloak Client Secret: {self.nextcloud_keycloak_client_secret}
Leos360 Portal Keycloak Client Secret: {self.leos360portal_keycloak_client_secret}
"""

        credentials_file = secrets_dir / "credentials.txt"
        credentials_file.write_text(credentials_content)
        os.chmod(credentials_file, SECRET_FILE_PERMISSIONS)
        print_success("Passwords generated and saved to credentials file")

    # =========================================================================
    # DIRECTORY STRUCTURE METHODS
    # =========================================================================

    def create_directory_structure(self) -> None:
        """
        Create directory structure for tenant.
        """
        print_step(f"Creating directory structure for {self.customer_name}...")

        # Base directories for all services
        base_services = ["nextcloud", "keycloak", "lldap", "dovecot", "postfix", "db", "redis"]
        for service in base_services:
            self.ensure_directory(self.config_base / service)

        # Redis specific structure
        self.ensure_directory(self.config_base / "redis" / "data")

        # Nextcloud specific structure
        nextcloud_dirs = ["data", "config", "custom_apps", "templates", "setup", "html"]
        for subdir in nextcloud_dirs:
            self.ensure_directory(self.config_base / "nextcloud" / subdir)

        # Keycloak specific structure
        keycloak_dirs = ["data", "config"]
        for subdir in keycloak_dirs:
            self.ensure_directory(self.config_base / "keycloak" / subdir)

        # LLDAP specific structure
        lldap_dirs = [
            "data",
            "config/bootstrap/group-configs",
            "config/bootstrap/group-schemas",
            "config/bootstrap/user-configs",
            "config/bootstrap/user-schemas"
        ]
        for subdir in lldap_dirs:
            self.ensure_directory(self.config_base / "lldap" / subdir)

        # Dovecot specific structure
        dovecot_dirs = ["data/mail", "config/conf.d", "config/ldap", "config/ssl", "config/scripts"]
        for subdir in dovecot_dirs:
            self.ensure_directory(self.config_base / "dovecot" / subdir)

        # Postfix specific structure
        postfix_dirs = ["config/ldap", "config/main.cf.d", "data/spool"]
        for subdir in postfix_dirs:
            self.ensure_directory(self.config_base / "postfix" / subdir)

        # Set base directory permissions
        os.chmod(self.config_base, DIR_PERMISSIONS)
        print_success("Directory structure created successfully")

    # =========================================================================
    # ENVIRONMENT FILE METHODS
    # =========================================================================

    def create_env_file(self) -> None:
        """
        Create .env file with proper variable substitutions.
        """
        print_step("Creating environment file...")

        # Copy master .env template
        env_file = self.config_base / ".env"
        if not self.copy_and_set_permissions(self.master_base / ".env", env_file):
            print_error("Failed to copy master .env template")
            sys.exit(1)

        # Read the content
        env_content = env_file.read_text()

        # Define all variable replacements
        replacements = {
            "${CUSTOMER_NAME}": self.customer_name,
            "${CUSTOMER_DOMAIN}": self.customer_domain,
            "${BASE_DOMAIN}": self.base_domain,
            "${CONFIG_BASE}": str(self.config_base),
            "${DB_PASSWORD}": self.db_password,
            "${NEXTCLOUD_ADMIN_PASSWORD}": self.nextcloud_admin_password,
            "${KEYCLOAK_ADMIN_PASSWORD}": self.keycloak_admin_password,
            "${LLDAP_JWT_SECRET}": self.lldap_jwt_secret,
            "${LLDAP_KEY_SEED}": self.lldap_key_seed,
            "${LLDAP_ADMIN_PASS}": self.lldap_admin_pass,
            "${LLDAP_RO_PASS}": self.lldap_ro_pass,
            "${EXTERNAL_DB_HOST}": self.external_db_host,
            "${EXTERNAL_DB_PORT}": self.external_db_port,
            "${EXTERNAL_DB_USER}": f"{self.customer_name}_admin",
            "${EXTERNAL_NEXTCLOUD_DB}": f"{self.customer_name}_nextcloud",
            "${EXTERNAL_KEYCLOAK_DB}": f"{self.customer_name}_keycloak",
            "${EXTERNAL_LLDAP_DB}": f"{self.customer_name}_lldap",
            "${CUSTOMER_IP}": self.customer_ip,
            "${SIGNAL_SECRET}": self.signal_secret,
            "${REDIS_DB_INDEX}": self.redis_db_index,
            "${REDIS_SECRET}": self.redis_secret,
            "${NEXTCLOUD_KEYCLOAK_CLIENT_SECRET}": self.nextcloud_keycloak_client_secret,
            "${LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET}": self.leos360portal_keycloak_client_secret
        }

        # Replace variables
        env_content = self.replace_variables(env_content, replacements)

        # Write updated content
        env_file.write_text(env_content)
        print_success("Environment file created successfully")

    # =========================================================================
    # SSL SETUP METHODS
    # =========================================================================

    def setup_ssl(self, ssl_dir: Path) -> None:
        """
        Set up SSL certificates.

        Args:
            ssl_dir: Directory where SSL certificates should be copied
        """
        print_step("Setting up SSL certificates...")

        # Copy SSL certificates
        ssl_files_copied = 0
        for file_path in self.ssl_base.glob("*"):
            if file_path.is_file():
                if self.copy_and_set_permissions(file_path, ssl_dir / file_path.name):
                    ssl_files_copied += 1

        print_success(f"Copied {ssl_files_copied} SSL certificate files")

    # =========================================================================
    # SERVICE CONFIGURATION METHODS
    # =========================================================================

    def _setup_service_config(self, service_name: str,
                            config_files: Dict[str, Tuple[Path, Path, Optional[int]]],
                            replacements: Optional[Dict[str, str]] = None) -> None:
        """
        Base method for setting up service configurations.

        Args:
            service_name: Name of the service being configured
            config_files: Dictionary of config files to process with:
                key: Description of file
                value: Tuple of (source_path, target_path, optional_permissions)
            replacements: Optional variable replacements
        """
        print_step(f"Setting up {service_name} configuration...")

        files_processed = 0
        for _, (source, target, permissions) in config_files.items():
            if not source.exists():
                print_warning(f"{service_name} source file {source} not found")
                continue

            content = source.read_text()
            if replacements:
                content = self.replace_variables(content, replacements)

            target.parent.mkdir(parents=True, exist_ok=True)
            target.write_text(content)

            if permissions is not None:
                os.chmod(target, permissions)
            else:
                os.chmod(target, FILE_PERMISSIONS)

            files_processed += 1

        print_success(f"Processed {files_processed} {service_name} configuration files")

    def setup_nextcloud(self) -> None:
        """
        Set up Nextcloud configuration.
        """
        replacements = {
            "${REDIS_DB_INDEX}": self.redis_db_index,
            "${REDIS_SECRET}": self.redis_secret,
            "${SIGNAL_SECRET}": self.signal_secret,
            "${CUSTOMER_NAME}": self.customer_name,
            "${CUSTOMER_DOMAIN}": self.customer_domain,
            "${BASE_DOMAIN}": self.base_domain,
            "${KEYCLOAK_REALM}": self.keycloak_realm,
            "${NEXTCLOUD_KEYCLOAK_CLIENT_SECRET}": self.nextcloud_keycloak_client_secret,
            "${LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET}": self.leos360portal_keycloak_client_secret
        }

        config_files = {
            "setup script": (
                self.master_base / "nextcloud" / "50-setup-nc.sh",
                self.config_base / "nextcloud" / "setup" / "50-setup-nc.sh",
                SCRIPT_PERMISSIONS
            )
        }

        self._setup_service_config("Nextcloud", config_files, replacements)

    def setup_keycloak(self) -> None:
        """
        Set up Keycloak configuration.
        """
        config_files = {
            "start script": (
                self.master_base / "keycloak" / "start-script.sh",
                self.config_base / "keycloak" / "start-script.sh",
                SCRIPT_PERMISSIONS
            ),
            "dockerfile": (
                self.master_base / "keycloak" / "dockerfile",
                self.config_base / "keycloak" / "dockerfile",
                None
            )
        }

        self._setup_service_config("Keycloak", config_files)

    def setup_dovecot(self) -> None:
        """
        Set up Dovecot configuration with proper variable replacement.
        """
        print_step("Setting up Dovecot configuration...")

        # Copy main Dovecot config files
        self.copy_and_set_permissions(
            self.master_base / "dovecot" / "dovecot.conf",
            self.config_base / "dovecot" / "config" / "dovecot.conf"
        )

        self.copy_and_set_permissions(
            self.master_base / "dovecot" / "scripts" / "quota-warning.sh",
            self.config_base / "dovecot" / "config" / "scripts" / "quota-warning.sh",
            SCRIPT_PERMISSIONS
        )

        # Copy conf.d files
        conf_d_files = 0
        for file_path in (self.master_base / "dovecot" / "conf.d").glob("*"):
            if file_path.is_file():
                self.copy_and_set_permissions(
                    file_path,
                    self.config_base / "dovecot" / "config" / "conf.d" / file_path.name
                )
                conf_d_files += 1

        # Copy SSL files
        self.copy_and_set_permissions(
            self.master_base / "dovecot" / "ssl" / "dh.pem",
            self.config_base / "dovecot" / "config" / "ssl" / "dh.pem"
        )

        # Process LDAP configuration files with variable replacement
        ldap_replacements = {
            "${CUSTOMER_NAME}": self.customer_name,
            "${LLDAP_RO_PASS}": self.lldap_ro_pass,
            "${CUSTOMER_IP}": self.customer_ip
        }

        ldap_config_files = [
            ("dovecot-ldap-passdb.conf", "ldap"),
            ("dovecot-ldap-userdb.conf", "ldap")
        ]

        ldap_files_processed = 0
        for file_name, subdir in ldap_config_files:
            source_file = self.master_base / "dovecot" / file_name
            target_file = self.config_base / "dovecot" / "config" / subdir / file_name

            if source_file.exists():
                content = source_file.read_text()
                content = self.replace_variables(content, ldap_replacements)

                target_file.parent.mkdir(parents=True, exist_ok=True)
                target_file.write_text(content)
                os.chmod(target_file, FILE_PERMISSIONS)
                ldap_files_processed += 1
            else:
                print_warning(f"Dovecot source file {source_file} not found")

        print_success(f"Dovecot configuration completed - {conf_d_files} conf.d files, {ldap_files_processed} LDAP files")

    def setup_postfix(self) -> None:
        """
        Set up Postfix configuration with proper variable replacement.
        """
        print_step("Setting up Postfix configuration...")

        # Copy main configuration
        self.copy_and_set_permissions(
            self.master_base / "postfix" / "main.cf",
            self.config_base / "postfix" / "config" / "main.cf.d" / "main.cf"
        )

        # Process LDAP configuration files with variable replacement
        ldap_replacements = {
            "${CUSTOMER_NAME}": self.customer_name,
            "${LLDAP_RO_PASS}": self.lldap_ro_pass,
            "${CUSTOMER_IP}": self.customer_ip
        }

        ldap_config_files = [
            "virtual_domains.cf",
            "virtual_aliases.cf"
        ]

        ldap_files_processed = 0
        for file_name in ldap_config_files:
            source_file = self.master_base / "postfix" / "ldap" / file_name
            target_file = self.config_base / "postfix" / "config" / "ldap" / file_name

            if source_file.exists():
                content = source_file.read_text()
                content = self.replace_variables(content, ldap_replacements)

                target_file.parent.mkdir(parents=True, exist_ok=True)
                target_file.write_text(content)
                os.chmod(target_file, FILE_PERMISSIONS)
                ldap_files_processed += 1
            else:
                print_warning(f"Postfix source file {source_file} not found")

        print_success(f"Postfix configuration completed - {ldap_files_processed} LDAP files processed")

    def setup_database(self) -> None:
        """
        Set up database configuration.
        """
        print_step("Setting up database configuration...")

        db_setup_file = self.config_base / "db" / f"db_setup_{self.customer_name}.sql"
        self.ensure_directory(self.config_base / "db")

        template_file = self.master_base / "db" / "db_setup.sql.template"

        if template_file.exists():
            sql_content = template_file.read_text()

            # Define database-specific replacements
            db_replacements = {
                "${DB_ADMIN_USER}": f"{self.customer_name}_admin",
                "${DB_NEXTCLOUD}": f"{self.customer_name}_nextcloud",
                "${DB_KEYCLOAK}": f"{self.customer_name}_keycloak",
                "${DB_LLDAP}": f"{self.customer_name}_lldap",
                "${DB_PASSWORD}": self.db_password,
                "${CUSTOMER_IP}": self.customer_ip,
            }

            sql_content = self.replace_variables(sql_content, db_replacements)
            db_setup_file.write_text(sql_content)
            os.chmod(db_setup_file, FILE_PERMISSIONS)
            print_success("Database setup script created successfully")
        else:
            print_warning(f"Database template file {template_file} not found")

    def setup_lldap(self) -> None:
        """
        Set up LLDAP configuration.
        """
        print_step("Setting up LLDAP configuration...")

        # Create directory structure
        lldap_bootstrap_dir = self.config_base / "lldap" / "config" / "bootstrap"

        # Create subdirectories
        for subdir in ["group-configs", "group-schemas", "user-configs", "user-schemas"]:
            self.ensure_directory(lldap_bootstrap_dir / subdir)

        # Copy bootstrap files
        bootstrap_files = [
            ("group-configs/groups.json", "group-configs/groups.json"),
            ("group-schemas/group-schemas.json", "group-schemas/group-schemas.json"),
            ("user-configs/users.json", "user-configs/users.json"),
            ("user-schemas/custom-attributes.json", "user-schemas/custom-attributes.json")
        ]

        files_copied = 0
        for src_rel, dst_rel in bootstrap_files:
            source = self.master_base / "lldap" / "bootstrap" / src_rel
            target = lldap_bootstrap_dir / dst_rel

            if source.exists():
                if self.copy_and_set_permissions(source, target):
                    files_copied += 1
            else:
                print_warning(f"LLDAP source file {source} not found")

        # Replace variables in the user configuration file
        user_config_file = lldap_bootstrap_dir / "user-configs" / "users.json"

        if user_config_file.exists():
            content = user_config_file.read_text()

            lldap_replacements = {
                "${CUSTOMER_NAME}": self.customer_name,
                "${LLDAP_ADMIN_PASS}": self.lldap_admin_pass,
                "${LLDAP_RO_PASS}": self.lldap_ro_pass,
                "${CUSTOMER_IP}": self.customer_ip
            }

            content = self.replace_variables(content, lldap_replacements)
            user_config_file.write_text(content)

        print_success(f"LLDAP configuration completed - {files_copied} bootstrap files copied")

    def setup_redis(self) -> None:
        """
        Set up Redis configuration.
        """
        print_step("Setting up Redis configuration...")

        # Create Redis data directory
        self.ensure_directory(self.config_base / "redis" / "data")
        print_success("Redis configuration completed")

    # =========================================================================
    # VERIFICATION AND COORDINATION METHODS
    # =========================================================================

    def verify_setup(self) -> None:
        """
        Verify the setup by checking required files and directories.

        Raises:
            SystemExit: If verification fails
        """
        print_step("Verifying setup...")

        required_items = [
            self.config_base / ".env",
            self.config_base / "docker-compose.yml",
            self.config_base / "nextcloud",
            self.config_base / "keycloak",
            self.config_base / "lldap",
            self.config_base / "dovecot",
            self.config_base / "postfix"
        ]

        missing_items = []
        for item in required_items:
            if not item.exists():
                missing_items.append(str(item))

        if missing_items:
            print_error("Setup verification failed - missing items:")
            for item in missing_items:
                print_error(f"  - {item}")
            sys.exit(1)

        print_success(f"Setup verification completed successfully for {self.customer_name}")

    def setup_services(self) -> None:
        """
        Coordinator function for setting up all services.
        """
        print_header("Setting up service configurations")

        # Create common SSL directory
        ssl_dir = self.config_base / "ssl"
        self.ensure_directory(ssl_dir)
        self.setup_ssl(ssl_dir)

        # Setup individual services
        self.setup_nextcloud()
        self.setup_keycloak()
        self.setup_lldap()
        self.setup_dovecot()
        self.setup_postfix()
        self.setup_database()
        self.setup_redis()

        print_success("All service configurations completed")

    def cleanup(self) -> None:
        """
        Clean up in case of error.
        """
        if self.config_base.exists():
            print_warning(f"Cleaning up {self.config_base} due to error...")
            shutil.rmtree(self.config_base, ignore_errors=True)

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self) -> bool:
        """
        Main execution function that orchestrates the entire tenant setup process.

        Returns:
            True if setup completed successfully, False otherwise
        """
        print_header(f"LEOS360 Tenant Setup - {self.customer_name}")
        print_step(f"Starting tenant setup for {self.customer_name}...")

        try:
            # Step 1: Validate prerequisites
            self.check_prerequisites()

            # Step 2: Create base directory and allocate resources
            self.config_base.mkdir(parents=True, exist_ok=True)
            self.setup_nextfreeip()

            # Step 3: Generate secure passwords
            self.generate_passwords()

            # Step 4: Create directory structure
            self.create_directory_structure()

            # Step 5: Create environment file
            self.create_env_file()

            # Step 6: Setup all services
            self.setup_services()

            # Step 7: Copy docker-compose.yml
            print_step("Copying Docker Compose configuration...")
            if not self.copy_and_set_permissions(
                self.master_base / "docker-compose.yml",
                self.config_base / "docker-compose.yml"
            ):
                print_error("Failed to copy Docker Compose configuration")
                return False

            # Step 8: Verify setup
            self.verify_setup()

            print_header("SETUP COMPLETED SUCCESSFULLY")
            print_success(f"Tenant {self.customer_name} has been configured successfully!")
            print_success(f"Configuration directory: {self.config_base}")
            print_success(f"Customer IP: {self.customer_ip}")
            print_success(f"Redis DB Index: {self.redis_db_index}")

            return True

        except Exception as e:
            print_error(f"Setup failed: {str(e)}")
            self.cleanup()
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup tenant configuration for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage1_tenant_config.py example-customer
  python3 stage1_tenant_config.py test-tenant-01

Requirements:
  - Must be run as root
  - Master templates must exist in /mnt/storage/setup
  - Target tenant directory must not already exist
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (lowercase alphanumeric with hyphens only)'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    try:
        # Create and run setup
        setup = TenantConfigSetup(args.customer_name)
        success = setup.run()

        if not success:
            sys.exit(1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()