#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 4: Portal SSO Setup
=============================================

This script handles the Portal SSO setup for a new tenant in the LEOS360 platform.
It creates organizations, identity providers, and configures SSO integration
with the central LEOS360 portal authentication system.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Stage 1, 2, and 3 must be completed successfully
- Stage 4 Keycloak setup must be completed
- Central SSO portal must be accessible
- Environment variables must be properly configured

Usage:
    python3 stage4_tenant_portal.py <customer_name> [options]

Example:
    python3 stage4_tenant_portal.py example-customer
    python3 stage4_tenant_portal.py example-customer --status
    python3 stage4_tenant_portal.py example-customer --export
"""

import os
import sys
import json
import requests
import time
import argparse
from pathlib import Path
from typing import Dict, Optional, Any
from dotenv import load_dotenv

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 4: Portal SSO Setup"

# File paths
BASE_TENANT_PATH = Path("/mnt/storage/tenants")
SSO_SETUP_PATH = Path("/mnt/storage/setup/keycloak")

# Portal SSO configuration
SSO_URL = "https://sso.leos360.com"
REALM_NAME = "leos360"
DEFAULT_VERIFY_SSL = True
API_TIMEOUT = 30


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


# =============================================================================
# MAIN CLASS IMPLEMENTATION
# =============================================================================

class TenantPortalSSOSetup:
    """
    Handles Portal SSO setup for a new tenant in the LEOS360 platform.

    This class manages the complete Portal SSO setup process including:
    - Keycloak admin authentication and token management
    - Organization creation and configuration
    - Identity provider setup and linking
    - SSO integration with central LEOS360 portal
    - Status checking and export operations
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant Portal SSO setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValueError: If customer_name is invalid
            FileNotFoundError: If required files are missing
        """
        self.customer_name = customer_name
        print_step(f"Initializing Portal SSO setup for: {customer_name}")

        # Configuration
        self.verify_ssl = DEFAULT_VERIFY_SSL
        self.keycloak_admin_username = None
        self.keycloak_admin_password = None
        self.access_token = None

        # Customer configuration
        self.customer_dir = BASE_TENANT_PATH / customer_name
        self.env_file = self.customer_dir / ".env"
        self.sso_env_file = SSO_SETUP_PATH / ".env"

        # Environment variables (will be loaded later)
        self.customer_domain = None
        self.kc_hostname = None
        self.keycloak_realm = None
        self.client_secret = None

        # Validate customer directory exists
        if not self.customer_dir.exists():
            raise FileNotFoundError(f"Customer directory does not exist: {self.customer_dir}")

        if not self.env_file.exists():
            raise FileNotFoundError(f"Customer .env file not found: {self.env_file}")

        if not self.sso_env_file.exists():
            raise FileNotFoundError(f"SSO .env file not found: {self.sso_env_file}")

    def load_env_credentials(self) -> None:
        """Load Keycloak admin credentials from both customer and SSO portal env files."""
        print_step("Loading environment credentials...")

        # Load customer env file
        load_dotenv(self.env_file)

        # Load SSO portal env file (override=True for admin credentials)
        load_dotenv(self.sso_env_file, override=True)

        # Get admin credentials
        self.keycloak_admin_username = os.getenv("KEYCLOAK_ADMIN_USERNAME")
        self.keycloak_admin_password = os.getenv("KEYCLOAK_ADMIN_PASSWORD")

        if not self.keycloak_admin_username or not self.keycloak_admin_password:
            raise ValueError("KEYCLOAK_ADMIN_USERNAME and KEYCLOAK_ADMIN_PASSWORD must be set in environment files")

        # Get customer configuration
        self.customer_domain = os.getenv("CUSTOMER_DOMAIN")
        self.kc_hostname = os.getenv("KC_HOSTNAME")
        self.keycloak_realm = os.getenv("KEYCLOAK_REALM")
        self.client_secret = os.getenv("LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET")

        # Validate required customer configuration
        if not self.customer_domain:
            raise ValueError("CUSTOMER_DOMAIN must be set in customer .env file")
        if not self.kc_hostname:
            raise ValueError("KC_HOSTNAME must be set in customer .env file")
        if not self.keycloak_realm:
            raise ValueError("KEYCLOAK_REALM must be set in customer .env file")
        if not self.client_secret:
            raise ValueError("LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET must be set in customer .env file")

        print_success("Environment credentials loaded successfully")

    def get_keycloak_token(self) -> str:
        """Get admin access token from Keycloak."""
        print_step("Obtaining Keycloak admin token...")

        url = f"{SSO_URL}/realms/master/protocol/openid-connect/token"

        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        data = {
            "client_id": "admin-cli",
            "username": self.keycloak_admin_username,
            "password": self.keycloak_admin_password,
            "grant_type": "password"
        }

        try:
            response = requests.post(url, headers=headers, data=data, verify=self.verify_ssl, timeout=API_TIMEOUT)
            response.raise_for_status()
            token = response.json().get("access_token")

            if not token:
                raise ValueError("No access token received from Keycloak")

            self.access_token = token
            print_success("Keycloak admin token obtained successfully")
            return token

        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"Failed to get access token: {e}")

    def check_organization_exists(self, org_name: str) -> bool:
        """Check if organization already exists in Keycloak."""
        print_step(f"Checking if organization '{org_name}' exists...")

        url = f"{SSO_URL}/admin/realms/{REALM_NAME}/organizations/{org_name}"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.get(url, headers=headers, verify=self.verify_ssl, timeout=API_TIMEOUT)
            exists = response.status_code == 200
            if exists:
                print_success(f"Organization '{org_name}' already exists")
            else:
                print_step(f"Organization '{org_name}' does not exist")
            return exists
        except requests.exceptions.RequestException as e:
            print_warning(f"Error checking organization existence: {e}")
            return False

    def create_organization(self, org_name: str, domain: str) -> bool:
        """Create a new organization in Keycloak with domain."""
        print_step(f"Creating organization '{org_name}' with domain '{domain}'...")

        if self.check_organization_exists(org_name):
            print_success(f"Organization '{org_name}' already exists, skipping creation")
            return True

        url = f"{SSO_URL}/admin/realms/{REALM_NAME}/organizations"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        org_data = {
            "name": org_name,
            "enabled": True,
            "domains": [{"name": domain, "verified": True}]
        }

        try:
            response = requests.post(url, headers=headers, json=org_data, verify=self.verify_ssl, timeout=API_TIMEOUT)
            response.raise_for_status()
            time.sleep(1)  # Wait for organization to be available
            print_success(f"Organization '{org_name}' created successfully")
            return True
        except requests.exceptions.RequestException as e:
            print_error(f"Error creating organization: {e}")
            return False

    def check_identity_provider_exists(self, idp_alias: str) -> bool:
        """Check if identity provider already exists in Keycloak."""
        print_step(f"Checking if identity provider '{idp_alias}' exists...")

        url = f"{SSO_URL}/admin/realms/{REALM_NAME}/identity-provider/instances/{idp_alias}"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.get(url, headers=headers, verify=self.verify_ssl, timeout=API_TIMEOUT)
            exists = response.status_code == 200
            if exists:
                print_success(f"Identity provider '{idp_alias}' already exists")
            else:
                print_step(f"Identity provider '{idp_alias}' does not exist")
            return exists
        except requests.exceptions.RequestException as e:
            print_warning(f"Error checking identity provider existence: {e}")
            return False

    def create_identity_provider(self, idp_alias: str) -> bool:
        """Create OIDC identity provider with complete configuration."""
        print_step(f"Creating identity provider '{idp_alias}'...")

        if self.check_identity_provider_exists(idp_alias):
            print_success(f"Identity provider '{idp_alias}' already exists, skipping creation")
            return True

        url = f"{SSO_URL}/admin/realms/{REALM_NAME}/identity-provider/instances"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        idp_config = {
            "alias": idp_alias,
            "displayName": self.customer_domain,
            "providerId": "keycloak-oidc",
            "enabled": True,
            "updateProfileFirstLoginMode": "on",
            "trustEmail": False,
            "storeToken": False,
            "addReadTokenRoleOnCreate": False,
            "authenticateByDefault": False,
            "linkOnly": False,
            "hideOnLogin": True,
            "config": {
                "tokenUrl": f"{self.kc_hostname}/realms/{self.keycloak_realm}/protocol/openid-connect/token",
                "acceptsPromptNoneForwardFromClient": "false",
                "jwksUrl": f"{self.kc_hostname}/realms/{self.keycloak_realm}/protocol/openid-connect/certs",
                "isAccessTokenJWT": "true",
                "filteredByClaim": "false",
                "backchannelSupported": "false",
                "caseSensitiveOriginalUsername": "false",
                "issuer": f"{self.kc_hostname}/realms/{self.keycloak_realm}",
                "pkceMethod": "S256",
                "loginHint": "true",
                "clientAuthMethod": "client_secret_post",
                "syncMode": "LEGACY",
                "clientSecret": self.client_secret,
                "allowedClockSkew": "0",
                "userInfoUrl": f"{self.kc_hostname}/realms/{self.keycloak_realm}/protocol/openid-connect/userinfo",
                "validateSignature": "true",
                "clientId": "leos360portal",
                "uiLocales": "false",
                "disableNonce": "false",
                "useJwksUrl": "true",
                "kc.org.domain": self.customer_domain,
                "kc.org.broker.redirect.mode.email-matches": "true",
                "sendClientIdOnLogout": "false",
                "pkceEnabled": "true",
                "metadataDescriptorUrl": f"{self.kc_hostname}/realms/{self.keycloak_realm}/.well-known/openid-configuration",
                "authorizationUrl": f"{self.kc_hostname}/realms/{self.keycloak_realm}/protocol/openid-connect/auth",
                "disableUserInfo": "false",
                "logoutUrl": f"{self.kc_hostname}/realms/{self.keycloak_realm}/protocol/openid-connect/logout",
                "sendIdTokenOnLogout": "true",
                "passMaxAge": "false"
            }
        }

        try:
            response = requests.post(url, headers=headers, json=idp_config, verify=self.verify_ssl, timeout=API_TIMEOUT)
            response.raise_for_status()
            print_success(f"Identity provider '{idp_alias}' created successfully")
            return True
        except requests.exceptions.RequestException as e:
            print_error(f"Error creating identity provider: {e}")
            return False

    def export_identity_provider(self, idp_alias: str) -> Optional[Dict[str, Any]]:
        """Export existing identity provider configuration."""
        print_step(f"Exporting identity provider '{idp_alias}' configuration...")

        url = f"{SSO_URL}/admin/realms/{REALM_NAME}/identity-provider/instances/{idp_alias}"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.get(url, headers=headers, verify=self.verify_ssl, timeout=API_TIMEOUT)
            response.raise_for_status()

            idp_config = response.json()
            print_header(f"Identity Provider '{idp_alias}' Configuration")
            print(json.dumps(idp_config, indent=2))
            print_success(f"Identity provider '{idp_alias}' configuration exported successfully")
            return idp_config
        except requests.exceptions.RequestException as e:
            print_error(f"Error exporting identity provider: {e}")
            if hasattr(e, 'response') and e.response:
                print_error(f"Response Status Code: {e.response.status_code}")
                print_error(f"Response Text: {e.response.text}")
            return None

    def list_identity_providers(self) -> Optional[list]:
        """List all identity providers in the realm."""
        print_step("Listing all identity providers...")

        url = f"{SSO_URL}/admin/realms/{REALM_NAME}/identity-provider/instances"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.get(url, headers=headers, verify=self.verify_ssl, timeout=API_TIMEOUT)
            response.raise_for_status()

            idp_list = response.json()
            print_header(f"Identity Providers in realm '{REALM_NAME}'")
            for idp in idp_list:
                print(f"- Alias: {idp.get('alias')}, Provider: {idp.get('providerId')}, Enabled: {idp.get('enabled')}")
            print_success(f"Found {len(idp_list)} identity providers")
            return idp_list
        except requests.exceptions.RequestException as e:
            print_error(f"Error listing identity providers: {e}")
            if hasattr(e, 'response') and e.response:
                print_error(f"Response Status Code: {e.response.status_code}")
                print_error(f"Response Text: {e.response.text}")
            return None

    def get_organization_id(self, org_name: str) -> Optional[str]:
        """Get the internal ID of an organization by name."""
        print_step(f"Getting organization ID for '{org_name}'...")

        url = f"{SSO_URL}/admin/realms/{REALM_NAME}/organizations"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.get(url, headers=headers, verify=self.verify_ssl, timeout=API_TIMEOUT)
            response.raise_for_status()

            organizations = response.json()
            for org in organizations:
                if org.get('name') == org_name:
                    org_id = org.get('id')
                    print_success(f"Found organization ID: {org_id}")
                    return org_id

            print_warning(f"Organization '{org_name}' not found")
            return None
        except requests.exceptions.RequestException as e:
            print_error(f"Error getting organization ID: {e}")
            return None

    def link_idp_to_organization(self, org_id: str, idp_alias: str) -> bool:
        """Link an identity provider to an organization using organization ID."""
        print_step(f"Linking identity provider '{idp_alias}' to organization '{org_id}'...")

        url = f"{SSO_URL}/admin/realms/{REALM_NAME}/organizations/{org_id}/identity-providers"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(url, headers=headers, json=idp_alias, verify=self.verify_ssl, timeout=API_TIMEOUT)
            response.raise_for_status()
            print_success(f"Identity provider '{idp_alias}' linked to organization successfully")
            return True
        except requests.exceptions.RequestException as e:
            print_error(f"Error linking identity provider to organization: {e}")
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str, idp_alias: Optional[str] = None) -> bool:
        """
        Main execution function that orchestrates the Portal SSO setup process.

        Args:
            operation: Operation to perform ('setup', 'export', or 'list')
            idp_alias: Identity provider alias for export operation

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 Portal SSO Setup - {self.customer_name}")
        print_step(f"Starting Portal SSO {operation} for {self.customer_name}...")

        try:
            # Step 1: Load environment credentials
            self.load_env_credentials()

            # Step 2: Get Keycloak admin token
            self.get_keycloak_token()

            # Step 3: Perform requested operation
            if operation == "export":
                if idp_alias:
                    result = self.export_identity_provider(idp_alias)
                    return result is not None
                else:
                    result = self.list_identity_providers()
                    return result is not None

            elif operation == "list":
                result = self.list_identity_providers()
                return result is not None

            else:  # default: setup
                # Step 3a: Create organization
                if not self.create_organization(self.customer_name, self.customer_domain):
                    print_error("Failed to create organization")
                    return False

                # Step 3b: Create identity provider
                if not self.create_identity_provider(self.customer_name):
                    print_error("Failed to create identity provider")
                    return False

                # Step 3c: Link identity provider to organization
                org_id = self.get_organization_id(self.customer_name)
                if not org_id:
                    print_error("Failed to get organization ID for linking")
                    return False

                if not self.link_idp_to_organization(org_id, self.customer_name):
                    print_error("Failed to link identity provider to organization")
                    return False

                print_header("OPERATION COMPLETED SUCCESSFULLY")
                print_success(f"Portal SSO setup for {self.customer_name} completed successfully!")
                return True

        except Exception as e:
            print_error(f"Portal SSO {operation} failed: {str(e)}")
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute Portal SSO setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup Portal SSO for LEOS360 platform tenant',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage4_tenant_portal.py example-customer
  python3 stage4_tenant_portal.py example-customer --export
  python3 stage4_tenant_portal.py example-customer --export idp-alias

Requirements:
  - Stage 1, 2, and 3 must be completed successfully
  - Stage 4 Keycloak setup must be completed
  - Central SSO portal must be accessible
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match previous stages)'
    )
    parser.add_argument(
        '--export',
        nargs='?',
        const='',
        help='Export identity provider configuration (optionally specify alias)'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation and parameters
    if args.export is not None:
        operation = "export"
        idp_alias = args.export if args.export else None
    else:
        operation = "setup"
        idp_alias = None

    try:
        # Create and run Portal SSO setup
        setup = TenantPortalSSOSetup(args.customer_name)
        success = setup.run(operation, idp_alias)

        if not success:
            sys.exit(1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except FileNotFoundError as e:
        print_error(f"Required file not found: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()