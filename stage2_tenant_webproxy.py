#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 2: Web Proxy Setup
===========================================

This script handles the web proxy configuration for a new tenant in the LEOS360 platform.
It configures the reverse proxy to route traffic for the customer domain to the appropriate
backend services using the web proxy API.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 must be completed successfully
- Web proxy API must be accessible at ************:5000
- Customer IP must be allocated and available in .env file

Usage:
    python3 stage2_tenant_webproxy.py <customer_name> [options]

Examples:
    python3 stage2_tenant_webproxy.py example-customer
    python3 stage2_tenant_webproxy.py example-customer --ip ***********
    python3 stage2_tenant_webproxy.py example-customer --delete
"""

import sys
import argparse
from pathlib import Path
from typing import Optional, Dict, Any
import requests

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 2: Web Proxy Setup"

# Web proxy API configuration
WEBPROXY_API_BASE = "http://************:5000"
WEBPROXY_ADD_ENDPOINT = "/webproxy/add"
WEBPROXY_DELETE_ENDPOINT = "/webproxy/del"
API_TIMEOUT = 10

# File paths
BASE_TENANT_PATH = Path("/mnt/storage/tenants")


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


# =============================================================================
# MAIN WEB PROXY SETUP CLASS
# =============================================================================

class TenantWebProxySetup:
    """
    Handles web proxy setup for a new tenant in the LEOS360 platform.

    This class manages the complete web proxy setup process including:
    - Reading customer IP from environment file
    - Configuring reverse proxy routing
    - API communication with web proxy service
    - Error handling and validation
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant web proxy setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValueError: If customer_name is invalid
        """
        self.customer_name = customer_name
        print_step(f"Initializing web proxy setup for: {customer_name}")

        # File paths
        self.env_file = BASE_TENANT_PATH / customer_name / ".env"

        # API configuration
        self.api_base = WEBPROXY_API_BASE
        self.add_url = f"{self.api_base}{WEBPROXY_ADD_ENDPOINT}/{customer_name}"
        self.delete_url = f"{self.api_base}{WEBPROXY_DELETE_ENDPOINT}/{customer_name}"

        # Runtime variables
        self.customer_ip = ""

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def get_customer_ip_from_env(self) -> str:
        """
        Get customer IP from .env file.

        Returns:
            Customer IP address

        Raises:
            FileNotFoundError: If .env file doesn't exist
            ValueError: If CUSTOMER_IP not found in .env file
        """
        if not self.env_file.exists():
            raise FileNotFoundError(f"Environment file does not exist: {self.env_file}")

        try:
            with open(self.env_file, 'r') as f:
                for line in f:
                    if line.startswith('CUSTOMER_IP='):
                        ip = line.strip().split('=', 1)[1]
                        print_step(f"Found customer IP in environment file: {ip}")
                        return ip

            raise ValueError(f"CUSTOMER_IP not found in {self.env_file}")

        except Exception as e:
            print_error(f"Could not read CUSTOMER_IP from {self.env_file}: {str(e)}")
            raise

    def make_api_request(self, method: str, url: str, payload: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Make an API request to the web proxy service.

        Args:
            method: HTTP method (POST, DELETE)
            url: API endpoint URL
            payload: Optional request payload

        Returns:
            API response data or None if failed

        Raises:
            Exception: If API request fails
        """
        headers = {"Content-Type": "application/json"}

        try:
            print_step(f"Making {method} request to {url}")

            if payload:
                response = requests.request(method, url, json=payload, headers=headers, timeout=API_TIMEOUT)
            else:
                response = requests.request(method, url, headers=headers, timeout=API_TIMEOUT)

            if response.status_code != 200:
                print_error(f"API call failed to {url}, response: {response.text}")
                raise Exception(f"Failed API call to {url}")

            print_success(f"API call successful to {url}")
            return response.json() if response.content else None

        except requests.exceptions.RequestException as e:
            print_error(f"Failed to connect to API: {str(e)}")
            raise

    # =========================================================================
    # WEB PROXY OPERATION METHODS
    # =========================================================================

    def setup_webproxy(self, customer_ip: Optional[str] = None) -> bool:
        """
        Set up web proxy configuration for this customer.

        Args:
            customer_ip: Optional customer IP address (will read from .env if not provided)

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Setting up web proxy for customer: {self.customer_name}")

        # Get customer IP if not provided
        if customer_ip:
            self.customer_ip = customer_ip
            print_step(f"Using provided customer IP: {customer_ip}")
        else:
            self.customer_ip = self.get_customer_ip_from_env()

        # Prepare API payload
        payload = {"ip": self.customer_ip}

        try:
            self.make_api_request("POST", self.add_url, payload)
            print_success(f"Web proxy setup completed for {self.customer_name} with IP {self.customer_ip}")
            return True

        except Exception as e:
            print_error(f"Web proxy setup failed: {str(e)}")
            return False

    def delete_webproxy(self) -> bool:
        """
        Delete web proxy configuration for this customer.

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Deleting web proxy for customer: {self.customer_name}")

        try:
            self.make_api_request("DELETE", self.delete_url)
            print_success(f"Web proxy deletion completed for {self.customer_name}")
            return True

        except Exception as e:
            print_error(f"Web proxy deletion failed: {str(e)}")
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str, customer_ip: Optional[str] = None) -> bool:
        """
        Main execution function that orchestrates the web proxy setup process.

        Args:
            operation: Operation to perform ('setup' or 'delete')
            customer_ip: Optional customer IP address

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 Web Proxy Configuration - {self.customer_name}")
        print_step(f"Starting web proxy {operation} for {self.customer_name}...")

        try:
            if operation == "delete":
                success = self.delete_webproxy()
            else:  # default: setup
                success = self.setup_webproxy(customer_ip)

            if success:
                print_header("OPERATION COMPLETED SUCCESSFULLY")
                print_success(f"Web proxy {operation} for {self.customer_name} completed successfully!")
                return True
            else:
                print_error(f"Web proxy {operation} failed for {self.customer_name}")
                return False

        except Exception as e:
            print_error(f"Web proxy {operation} failed: {str(e)}")
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute web proxy setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup tenant web proxy for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage2_tenant_webproxy.py example-customer
  python3 stage2_tenant_webproxy.py example-customer --ip ***********
  python3 stage2_tenant_webproxy.py example-customer --delete

Requirements:
  - Stage 1 must be completed successfully
  - Web proxy API must be accessible at ************:5000
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    parser.add_argument(
        '--ip',
        help='IP address of the customer (optional, will be read from .env if not provided)'
    )
    parser.add_argument(
        '--delete',
        action='store_true',
        help='Delete the web proxy for the customer'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    operation = "delete" if args.delete else "setup"

    try:
        # Create and run web proxy setup
        setup = TenantWebProxySetup(args.customer_name)
        success = setup.run(operation, args.ip)

        if not success:
            sys.exit(1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except FileNotFoundError as e:
        print_error(f"File not found: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Web proxy setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
