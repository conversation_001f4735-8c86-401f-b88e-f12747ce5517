# General Settings
CUSTOMER_NAME=${CUSTOMER_NAME}
CUSTOMER_DOMAIN=${CUSTOMER_DOMAIN}
BASE_DOMAIN=${BASE_DOMAIN}
leos360sso_url=https://sso.leos360.com
CUSTOMER_DIR=/mnt/storage/tenants/${CUSTOMER_NAME}
CONFIG_BASE=/mnt/storage/tenants/${CUSTOMER_NAME}
SETUP_BASE=/mnt/storage/setup/
CUSTOMER_IP=${CUSTOMER_IP}
# Timezone
TIMEZONE=Europe/Vienna
# External Database Settings
EXTERNAL_DB_HOST=************
EXTERNAL_DB_PORT=5432
EXTERNAL_DB_USER=${CUSTOMER_NAME}_admin
EXTERNAL_DB_PASSWORD=${DB_PASSWORD}
DB_ADMIN_PASSWORD=Yevid!Ejone5092
# Database Names
EXTERNAL_NEXTCLOUD_DB=${CUSTOMER_NAME}_nextcloud
EXTERNAL_KEYCLOAK_DB=${CUSTOMER_NAME}_keycloak
EXTERNAL_LLDAP_DB=${CUSTOMER_NAME}_lldap
# Nextcloud Settings
NEXTCLOUD_ADMIN_USER=admin
NEXTCLOUD_ADMIN_PASSWORD=${NEXTCLOUD_ADMIN_PASSWORD}
NEXTCLOUD_TRUSTED_DOMAINS=${CUSTOMER_NAME}.${BASE_DOMAIN}
NEXTCLOUD_OVERWRITEHOST=${CUSTOMER_NAME}.${BASE_DOMAIN}
# Mail Settings Nextcloud
MAIL_DOMAIN=${CUSTOMER_NAME}.${BASE_DOMAIN}
RELAYHOST=mail.${CUSTOMER_NAME}.${BASE_DOMAIN}
# TALK/COTURN
SIGNAL_SECRET=${SIGNAL_SECRET}
# REDIS
REDIS_DB_INDEX=${REDIS_DB_INDEX}
REDIS_PASSWORD=${REDIS_SECRET}
# Keycloak Settings
KC_BUILD=true
KEYCLOAK_DATABASE_HOST=${EXTERNAL_DB_HOST}
KEYCLOAK_DATABASE_PORT=${EXTERNAL_DB_PORT}
KEYCLOAK_DATABASE_NAME=${EXTERNAL_KEYCLOAK_DB}
KEYCLOAK_DATABASE_USER=${EXTERNAL_DB_USER}
KEYCLOAK_DATABASE_PASSWORD=${DB_PASSWORD}
KC_HOSTNAME=https://${CUSTOMER_NAME}-sso.${BASE_DOMAIN}
KEYCLOAK_ADMIN_USERNAME=admin
KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD}
KEYCLOAK_REALM=${CUSTOMER_DOMAIN}
# Keycloak Client Secrets und Mapping
NEXTCLOUD_KEYCLOAK_CLIENT_SECRET=${NEXTCLOUD_KEYCLOAK_CLIENT_SECRET}
LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET=${LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET}
LLDAP_GRUPPEN_KC_MAPPING=nextcloud:nc-users,nc-admins;leos360portal:nc-users,nc-admins

# LLDAP Settings
LLDAP_JWT_SECRET=${LLDAP_JWT_SECRET}
LLDAP_KEY_SEED=${LLDAP_KEY_SEED}
LLDAP_BASE_DN=dc=${CUSTOMER_NAME},dc=leos360,dc=cloud
LLDAP_ADMIN_PASS=${LLDAP_ADMIN_PASS}
LLDAP_RO_PASS=${LLDAP_RO_PASS}
LLDAP_DATABASE_URL=postgres://${EXTERNAL_DB_USER}:${DB_PASSWORD}@${EXTERNAL_DB_HOST}:${EXTERNAL_DB_PORT}/${EXTERNAL_LLDAP_DB}
LLDAP_UID=7654
LLDAP_GID=7654
