#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 4: LLDAP Setup
========================================

This script handles the LLDAP (Light LDAP) setup for a new tenant in the LEOS360 platform.
It executes bootstrap commands in the LLDAP Docker container via the Portainer API to
initialize the LDAP directory structure and configuration.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Stage 1, 2, and 3 must be completed successfully
- Docker stack must be deployed and running
- LLDAP container must be accessible via Portainer API
- Portainer API credentials must be configured

Usage:
    python3 stage4_tenant_lldap.py <customer_name> [options]

Examples:
    python3 stage4_tenant_lldap.py example-customer
    python3 stage4_tenant_lldap.py example-customer --status
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Any
import requests
import urllib3
from dotenv import load_dotenv

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 4: LLDAP Setup"

# File paths
DOCKER_CONFIG_PATH = Path("/mnt/storage/docker/.env")

# Portainer configuration
DEFAULT_ENDPOINT_ID = "11"
PORTAINER_PORT = 9443
API_TIMEOUT = 30

# LLDAP configuration
LLDAP_CONTAINER_SUFFIX = "lldap"
BOOTSTRAP_COMMAND = "./bootstrap.sh"

# Disable SSL warnings for Portainer API
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


def print_debug(message: str) -> None:
    """Print a formatted debug message."""
    print(f"[DEBUG] {message}")


# =============================================================================
# MAIN LLDAP SETUP CLASS
# =============================================================================

class TenantLLDAPSetup:
    """
    Handles LLDAP setup for a new tenant in the LEOS360 platform.

    This class manages the complete LLDAP setup process including:
    - Portainer API configuration and authentication
    - Docker container discovery and management
    - Command execution in LLDAP containers
    - Bootstrap script execution and monitoring
    - Error handling and status reporting
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant LLDAP setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValueError: If customer_name is invalid
        """
        self.customer_name = customer_name
        print_step(f"Initializing LLDAP setup for: {customer_name}")

        # Container configuration
        self.container_name = f"{customer_name}-{LLDAP_CONTAINER_SUFFIX}"

        # Portainer configuration (will be loaded later)
        self.portainer_config = {}

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def load_portainer_config(self) -> None:
        """
        Load Portainer configuration from environment file.

        Raises:
            FileNotFoundError: If config file doesn't exist
            ValueError: If required configuration is missing
        """
        if not DOCKER_CONFIG_PATH.exists():
            raise FileNotFoundError(f"Docker config file not found: {DOCKER_CONFIG_PATH}")

        load_dotenv(DOCKER_CONFIG_PATH)

        api_key = os.getenv("PORTAINER_API_KEY")
        host = os.getenv("PORTAINER_HOST")

        if not api_key or not host:
            raise ValueError("PORTAINER_API_KEY or PORTAINER_HOST not defined in config")

        self.portainer_config = {
            "api_key": api_key,
            "host": host,
            "endpoint_id": os.getenv("ENDPOINT_ID", DEFAULT_ENDPOINT_ID),
            "portainer_url": f"https://{host}:{PORTAINER_PORT}"
        }

        print_step("Portainer configuration loaded successfully")

    # =========================================================================
    # PORTAINER API METHODS
    # =========================================================================

    def make_api_request(self, method: str, url: str, data: Optional[Dict] = None,
                        headers: Optional[Dict] = None) -> requests.Response:
        """
        Make an API request to Portainer.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            url: API endpoint URL
            data: Optional request data
            headers: Optional custom headers

        Returns:
            Response object

        Raises:
            Exception: If API request fails
        """
        if headers is None:
            headers = {
                "X-API-Key": self.portainer_config["api_key"],
                "Content-Type": "application/json"
            }

        try:
            print_debug(f"Making {method} request to {url}")

            if method.upper() == "GET":
                response = requests.get(url, headers=headers, verify=False, timeout=API_TIMEOUT)
            elif method.upper() == "POST":
                response = requests.post(url, headers=headers, json=data,
                                       verify=False, timeout=API_TIMEOUT)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            print_debug(f"Response status: {response.status_code}")
            return response

        except requests.exceptions.RequestException as e:
            print_error(f"API request failed: {e}")
            raise

    def get_containers(self) -> List[Dict[str, Any]]:
        """
        Get list of containers from Portainer.

        Returns:
            List of container dictionaries

        Raises:
            Exception: If API request fails
        """
        endpoint_id = self.portainer_config["endpoint_id"]
        url = f"{self.portainer_config['portainer_url']}/api/endpoints/{endpoint_id}/docker/containers/json?all=true"

        response = self.make_api_request("GET", url)

        if response.status_code != 200:
            raise Exception(f"Failed to get containers: {response.status_code} - {response.text}")

        return response.json()

    def find_container_by_name(self, container_name: str) -> Optional[str]:
        """
        Find a container by name and return its ID.

        Args:
            container_name: Name of the container to find

        Returns:
            Container ID if found, None otherwise
        """
        print_step(f"Finding container: {container_name}")

        containers = self.get_containers()

        for container in containers:
            for name in container.get("Names", []):
                # Remove leading slash if present
                clean_name = name[1:] if name.startswith("/") else name
                if clean_name == container_name:
                    container_id = container.get("Id")
                    print_step(f"Found container ID: {container_id}")
                    return container_id

        print_warning(f"Container {container_name} not found")
        return None

    # =========================================================================
    # COMMAND EXECUTION METHODS
    # =========================================================================

    def execute_command_in_container(self, command: str) -> bool:
        """
        Execute a command in the LLDAP container.

        Args:
            command: Command to execute

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Executing command in container {self.container_name}: {command}")

        # Find the container
        container_id = self.find_container_by_name(self.container_name)
        if not container_id:
            print_error(f"Container {self.container_name} not found")
            return False

        try:
            # Create exec instance
            print_step("Creating exec instance")
            exec_id = self._create_exec_instance(container_id, command)
            if not exec_id:
                return False

            # Start the exec instance
            print_step("Starting exec instance")
            if not self._start_exec_instance(exec_id):
                return False

            # Check the result
            print_step("Checking exec result")
            return self._check_exec_result(exec_id)

        except Exception as e:
            print_error(f"Exception during command execution: {e}")
            return False

    def _create_exec_instance(self, container_id: str, command: str) -> Optional[str]:
        """
        Create an exec instance in the container.

        Args:
            container_id: ID of the container
            command: Command to execute

        Returns:
            Exec instance ID if successful, None otherwise
        """
        endpoint_id = self.portainer_config["endpoint_id"]
        url = f"{self.portainer_config['portainer_url']}/api/endpoints/{endpoint_id}/docker/containers/{container_id}/exec"

        payload = {
            "AttachStdin": False,
            "AttachStdout": True,
            "AttachStderr": True,
            "Tty": False,
            "Cmd": command if isinstance(command, list) else command.split()
        }

        response = self.make_api_request("POST", url, data=payload)

        if response.status_code != 201:
            print_error(f"Failed to create exec instance: {response.status_code} - {response.text}")
            return None

        exec_id = response.json().get("Id")
        print_debug(f"Created exec instance with ID: {exec_id}")
        return exec_id

    def _start_exec_instance(self, exec_id: str) -> bool:
        """
        Start an exec instance.

        Args:
            exec_id: ID of the exec instance

        Returns:
            True if successful, False otherwise
        """
        endpoint_id = self.portainer_config["endpoint_id"]
        url = f"{self.portainer_config['portainer_url']}/api/endpoints/{endpoint_id}/docker/exec/{exec_id}/start"

        payload = {
            "Detach": False,
            "Tty": False
        }

        response = self.make_api_request("POST", url, data=payload)

        if response.status_code != 200:
            print_error(f"Failed to start exec instance: {response.status_code} - {response.text}")
            return False

        return True

    def _check_exec_result(self, exec_id: str) -> bool:
        """
        Check the result of an exec instance.

        Args:
            exec_id: ID of the exec instance

        Returns:
            True if command succeeded, False otherwise
        """
        endpoint_id = self.portainer_config["endpoint_id"]
        url = f"{self.portainer_config['portainer_url']}/api/endpoints/{endpoint_id}/docker/exec/{exec_id}/json"

        response = self.make_api_request("GET", url)

        if response.status_code != 200:
            print_error(f"Failed to inspect exec instance: {response.status_code} - {response.text}")
            return False

        exec_result = response.json()
        exit_code = exec_result.get("ExitCode")

        if exit_code == 0:
            print_success(f"Command executed successfully in container {self.container_name}")
            return True
        else:
            print_error(f"Command failed with exit code {exit_code}")
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str = "bootstrap") -> bool:
        """
        Main execution function that orchestrates the LLDAP setup process.

        Args:
            operation: Operation to perform ('bootstrap' or 'status')

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 LLDAP Setup - {self.customer_name}")
        print_step(f"Starting LLDAP {operation} for {self.customer_name}...")

        try:
            # Step 1: Load Portainer configuration
            self.load_portainer_config()

            # Step 2: Perform requested operation
            if operation == "status":
                return self.show_container_status()
            else:  # default: bootstrap
                success = self.execute_command_in_container(BOOTSTRAP_COMMAND)

                if success:
                    print_header("OPERATION COMPLETED SUCCESSFULLY")
                    print_success(f"LLDAP bootstrap for {self.customer_name} completed successfully!")

                return success

        except Exception as e:
            print_error(f"LLDAP {operation} failed: {str(e)}")
            return False

    def show_container_status(self) -> bool:
        """
        Show status of the LLDAP container for this customer.

        Returns:
            True if container exists, False otherwise
        """
        print_step(f"Checking LLDAP container status for customer: {self.customer_name}")

        try:
            container_id = self.find_container_by_name(self.container_name)

            if container_id:
                print_success(f"LLDAP container {self.container_name} exists:")
                print(f"  - Container ID: {container_id}")
                print(f"  - Container Name: {self.container_name}")
                return True
            else:
                print_warning(f"LLDAP container {self.container_name} does not exist")
                return False

        except Exception as e:
            print_error(f"Failed to check container status: {e}")
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute LLDAP setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Execute LLDAP bootstrap for LEOS360 platform tenant',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage4_tenant_lldap.py example-customer
  python3 stage4_tenant_lldap.py example-customer --status

Requirements:
  - Stage 1, 2, and 3 must be completed successfully
  - Docker stack must be deployed and running
  - LLDAP container must be accessible via Portainer API
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match previous stages)'
    )
    parser.add_argument(
        '--status',
        action='store_true',
        help='Show status of the LLDAP container'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    operation = "status" if args.status else "bootstrap"

    try:
        # Create and run LLDAP setup
        setup = TenantLLDAPSetup(args.customer_name)
        success = setup.run(operation)

        if not success:
            sys.exit(1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except FileNotFoundError as e:
        print_error(f"File not found: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("LLDAP setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()