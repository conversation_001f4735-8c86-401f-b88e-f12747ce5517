<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tenant Deployment Infrastructure</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .lan-boundary {
            border: 3px dashed #e74c3c;
            border-radius: 15px;
            padding: 30px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            position: relative;
            margin-bottom: 30px;
        }
        
        .lan-label {
            position: absolute;
            top: -15px;
            left: 20px;
            background: #e74c3c;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .infrastructure-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .server {
            background: linear-gradient(145deg, #ffffff, #f1f2f6);
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .server::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }
        
        .server:hover::before {
            left: 100%;
        }
        
        .server:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            border-color: #3498db;
        }
        
        .deployment-server {
            grid-column: 1 / -1;
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            border-color: #d63031;
        }
        
        .portainer-server {
            background: linear-gradient(145deg, #74b9ff, #0984e3);
            color: white;
            border-color: #0984e3;
        }
        
        .sso-server {
            background: linear-gradient(145deg, #6c5ce7, #5f3dc4);
            color: white;
            border-color: #5f3dc4;
        }
        
        .server-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }
        
        .server-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .server-ip {
            background: rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 5px 10px;
            font-family: 'Courier New', monospace;
            margin-bottom: 10px;
            font-size: 0.9em;
        }
        
        .server-desc {
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .docker-stack-container {
            margin: 30px 0;
            background: linear-gradient(145deg, #2d3436, #636e72);
            border-radius: 15px;
            padding: 25px;
            color: white;
        }
        
        .docker-stack-title {
            text-align: center;
            font-size: 1.8em;
            margin-bottom: 25px;
            color: #74b9ff;
        }
        
        .stack-services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stack-service {
            background: linear-gradient(145deg, #0984e3, #74b9ff);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .stack-service:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0,0,0,0.3);
        }
        
        .service-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: block;
        }
        
        .service-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .service-desc {
            font-size: 0.8em;
            opacity: 0.9;
        }
        
        .stages-container {
            margin-top: 40px;
        }
        
        .stages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stage {
            background: linear-gradient(145deg, #74b9ff, #0984e3);
            color: white;
            border-radius: 15px;
            padding: 25px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .stage:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        
        .stage-1 { background: linear-gradient(145deg, #55a3ff, #2980b9); }
        .stage-2 { background: linear-gradient(145deg, #fd79a8, #e84393); }
        .stage-3 { background: linear-gradient(145deg, #fdcb6e, #e17055); }
        .stage-4 { background: linear-gradient(145deg, #6c5ce7, #5f3dc4); }
        
        .stage-number {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 3em;
            opacity: 0.3;
            font-weight: bold;
        }
        
        .stage-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .stage-actions {
            list-style: none;
        }
        
        .stage-actions li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .stage-actions li::before {
            content: '→';
            position: absolute;
            left: 0;
            color: rgba(255,255,255,0.8);
            font-weight: bold;
        }
        
        .api-connections {
            margin-top: 30px;
            text-align: center;
        }
        
        .api-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .connection-flows {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .connection-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            padding: 15px;
            background: rgba(116, 185, 255, 0.1);
            border-radius: 10px;
        }
        
        .flow-box {
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            font-size: 0.9em;
        }
        
        .flow-arrow {
            font-size: 1.2em;
            color: #e74c3c;
            font-weight: bold;
        }
        
        .stage-target {
            background: linear-gradient(145deg, #00b894, #00a085);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            font-size: 0.9em;
        }
        
        .portainer-target {
            background: linear-gradient(145deg, #74b9ff, #0984e3);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            font-size: 0.9em;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
            100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
        }
        
        .active-stage {
            animation: pulse 2s infinite;
        }
        
        .legend {
            margin-top: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #3498db;
        }
        
        .legend h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .legend-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Multi-Stage Tenant Deployment Infrastructure</h1>
        
        <div class="lan-boundary">
            <div class="lan-label">🔒 Separates LAN</div>
            
            <div class="infrastructure-grid">
                <div class="server deployment-server" data-server="deployment">
                    <span class="server-icon">🎯</span>
                    <div class="server-title">Deployment Server</div>
                    <div class="server-ip">IP: 1234</div>
                    <div class="server-desc">Central API Controller<br/>Orchestriert alle Deployment-Stages</div>
                </div>
                
                <div class="server portainer-server" data-server="portainer">
                    <span class="server-icon">🎛️</span>
                    <div class="server-title">Portainer Server</div>
                    <div class="server-ip">IP: 1234</div>
                    <div class="server-desc">Docker Management API<br/>Steuert alle Docker Hosts</div>
                </div>
                
                <div class="server sso-server" data-server="sso">
                    <span class="server-icon">🔐</span>
                    <div class="server-title">SSO/Keycloak Global</div>
                    <div class="server-ip">IP: 1234</div>
                    <div class="server-desc">Globales SSO System<br/>Zentrale Authentifizierung</div>
                </div>
                
                <div class="server" data-server="storage">
                    <span class="server-icon">📁</span>
                    <div class="server-title">Storage Server</div>
                    <div class="server-ip">IP: 1234</div>
                    <div class="server-desc">Stage1 & Tenant Data<br/>Konfigurationsspeicher</div>
                </div>
                
                <div class="server" data-server="database">
                    <span class="server-icon">🗄️</span>
                    <div class="server-title">DB Cluster</div>
                    <div class="server-ip">IP: 1234</div>
                    <div class="server-desc">Datenbank für alle Tenants<br/>Zentrale Datenhaltung</div>
                </div>
                
                <div class="server" data-server="dns">
                    <span class="server-icon">🌐</span>
                    <div class="server-title">DNS Server</div>
                    <div class="server-ip">IP: 1234</div>
                    <div class="server-desc">DNS für alle Tenants & Infra<br/>Domain-Management</div>
                </div>
                
                <div class="server" data-server="proxy">
                    <span class="server-icon">🔗</span>
                    <div class="server-title">WebProxy</div>
                    <div class="server-ip">IP: 1234</div>
                    <div class="server-desc">Globaler Proxy alle Tenants<br/>Load Balancing & Routing</div>
                </div>
                
                <div class="server" data-server="docker">
                    <span class="server-icon">🐳</span>
                    <div class="server-title">Docker Host</div>
                    <div class="server-ip">IP: 1234</div>
                    <div class="server-desc">Container für alle Tenants<br/>Microservices Orchestrierung</div>
                </div>
            </div>
        </div>
        
        <div class="docker-stack-container">
            <div class="docker-stack-title">🐳 Docker Stack - Tenant Services</div>
            <div class="stack-services">
                <div class="stack-service" data-service="nextcloud">
                    <span class="service-icon">☁️</span>
                    <div class="service-name">Nextcloud</div>
                    <div class="service-desc">File Sharing & Collaboration</div>
                </div>
                
                <div class="stack-service" data-service="lldap">
                    <span class="service-icon">📋</span>
                    <div class="service-name">LLDAP</div>
                    <div class="service-desc">LDAP Directory für Tenant</div>
                </div>
                
                <div class="stack-service" data-service="redis">
                    <span class="service-icon">⚡</span>
                    <div class="service-name">Redis</div>
                    <div class="service-desc">In-Memory Cache</div>
                </div>
                
                <div class="stack-service" data-service="keycloak">
                    <span class="service-icon">🔑</span>
                    <div class="service-name">Keycloak Tenant</div>
                    <div class="service-desc">Identity Management</div>
                </div>
                
                <div class="stack-service" data-service="dovecot">
                    <span class="service-icon">📬</span>
                    <div class="service-name">Dovecot</div>
                    <div class="service-desc">IMAP/POP3 Mail Server</div>
                </div>
                
                <div class="stack-service" data-service="postfix">
                    <span class="service-icon">📮</span>
                    <div class="service-name">Postfix</div>
                    <div class="service-desc">SMTP Mail Transfer</div>
                </div>
            </div>
        </div>
        
        <div class="stages-container">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">📋 Deployment Stages Ablauf</h2>
            
            <div class="stages-grid">
                <div class="stage stage-1" data-stage="1">
                    <div class="stage-number">1</div>
                    <div class="stage-title">🔧 Tenant Configuration Setup</div>
                    <ul class="stage-actions">
                        <li>Tenant-Konfiguration erstellen</li>
                        <li>Storage Server vorbereiten</li>
                        <li>Basis-Parameter definieren</li>
                        <li>SSO-Integration planen</li>
                    </ul>
                </div>
                
                <div class="stage stage-2" data-stage="2">
                    <div class="stage-number">2</div>
                    <div class="stage-title">🏗️ Database, DNS & WebProxy Setup</div>
                    <ul class="stage-actions">
                        <li>Database für Tenant einrichten</li>
                        <li>DNS-Einträge konfigurieren</li>
                        <li>WebProxy-Routing einrichten</li>
                        <li>SSO-Verbindung konfigurieren</li>
                    </ul>
                </div>
                
                <div class="stage stage-3" data-stage="3">
                    <div class="stage-number">3</div>
                    <div class="stage-title">🐳 Docker Stack Deployment</div>
                    <ul class="stage-actions">
                        <li>Portainer API: Stack erstellen</li>
                        <li>Alle Services deployen</li>
                        <li>Container-Netzwerk einrichten</li>
                        <li>Health Checks durchführen</li>
                    </ul>
                </div>
                
                <div class="stage stage-4" data-stage="4">
                    <div class="stage-title">⚙️ Service Configuration</div>
                    <div class="stage-number">4</div>
                    <ul class="stage-actions">
                        <li>Keycloak Tenant konfigurieren</li>
                        <li>LLDAP mit SSO verknüpfen</li>
                        <li>Nextcloud & Mail-Services</li>
                        <li>Redis Cache optimieren</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="api-connections">
            <div class="api-title">🔄 API-Steuerung & Datenfluss</div>
            
            <div class="connection-flows">
                <div class="connection-flow">
                    <div class="flow-box">Deployment Server</div>
                    <div class="flow-arrow">📡</div>
                    <div class="stage-target">Stage 1-2 API</div>
                    <div class="flow-arrow">🎯</div>
                    <div class="stage-target">Storage, DB, DNS, Proxy</div>
                </div>
                
                <div class="connection-flow">
                    <div class="flow-box">Deployment Server</div>
                    <div class="flow-arrow">📡</div>
                    <div class="portainer-target">Portainer API</div>
                    <div class="flow-arrow">🐳</div>
                    <div class="stage-target">Docker Host Stack</div>
                </div>
                
                <div class="connection-flow">
                    <div class="flow-box">Tenant Services</div>
                    <div class="flow-arrow">🔐</div>
                    <div class="portainer-target">SSO/Keycloak Global</div>
                    <div class="flow-arrow">🎯</div>
                    <div class="stage-target">Zentrale Authentifizierung</div>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <h3>🎨 System-Architektur Übersicht</h3>
            <div class="legend-grid">
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(145deg, #ff6b6b, #ee5a52);"></div>
                    <span>Central Management (Deployment)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(145deg, #74b9ff, #0984e3);"></div>
                    <span>Container Management (Portainer)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(145deg, #6c5ce7, #5f3dc4);"></div>
                    <span>Global SSO Services</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(145deg, #74b9ff, #f1f2f6);"></div>
                    <span>Infrastructure Services</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: linear-gradient(145deg, #0984e3, #74b9ff);"></div>
                    <span>Tenant Docker Stack</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Interactive hover effects for servers
        document.querySelectorAll('.server').forEach(server => {
            server.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            server.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // Interactive hover effects for stages
        document.querySelectorAll('.stage').forEach(stage => {
            stage.addEventListener('mouseenter', function() {
                this.classList.add('active-stage');
            });
            
            stage.addEventListener('mouseleave', function() {
                this.classList.remove('active-stage');
            });
        });
        
        // Interactive effects for stack services
        document.querySelectorAll('.stack-service').forEach(service => {
            service.addEventListener('click', function() {
                const serviceName = this.dataset.service;
                const descriptions = {
                    'nextcloud': 'File Sharing & Collaboration Platform\n• WebDAV, CalDAV, CardDAV\n• Office Integration\n• Benutzer-Management über LLDAP',
                    'lldap': 'Lightweight LDAP Directory\n• Benutzer- und Gruppenverwaltung\n• Integration mit Keycloak\n• LDAP-Backend für alle Services',
                    'redis': 'In-Memory Datenbank\n• Session-Storage\n• Cache für Nextcloud\n• Performance-Optimierung',
                    'keycloak': 'Identity & Access Management\n• Single Sign-On für Tenant\n• OAuth2, OIDC, SAML\n• Integration mit Global SSO',
                    'dovecot': 'IMAP/POP3 Mail Server\n• Mail-Zustellung\n• Sieve-Filter\n• Integration mit LLDAP',
                    'postfix': 'SMTP Mail Transfer Agent\n• Mail-Versand\n• Anti-Spam\n• Domain-Routing'
                };
                alert(`${this.querySelector('.service-name').textContent}\n\n${descriptions[serviceName]}`);
            });
        });
        
        // Simulate deployment flow animation
        let currentStage = 1;
        function simulateDeployment() {
            document.querySelectorAll('.stage').forEach(s => s.classList.remove('active-stage'));
            
            const stage = document.querySelector(`[data-stage="${currentStage}"]`);
            if (stage) {
                stage.classList.add('active-stage');
                currentStage = currentStage >= 4 ? 1 : currentStage + 1;
            }
        }
        
        // Auto-cycle through stages every 4 seconds
        setInterval(simulateDeployment, 4000);
        
        // Click handlers for servers
        document.querySelectorAll('.server').forEach(server => {
            server.addEventListener('click', function() {
                const serverType = this.dataset.server;
                const serverDescriptions = {
                    'deployment': 'Deployment Server\n\n• Zentrale Orchestrierung aller Stages\n• API-Controller für Infrastructure\n• Monitoring & Logging',
                    'portainer': 'Portainer Management\n\n• Docker Container Management\n• Stack Deployment via API\n• Container Health Monitoring\n• Volume & Network Management',
                    'sso': 'SSO/Keycloak Global\n\n• Zentrale Authentifizierung\n• Multi-Tenant SSO\n• Identity Federation\n• SAML, OIDC, OAuth2',
                    'storage': 'Storage Server\n\n• Tenant-Konfigurationen\n• File Storage für Nextcloud\n• Backup & Archivierung',
                    'database': 'Database Cluster\n\n• PostgreSQL für alle Tenants\n• High Availability\n• Backup & Replication',
                    'dns': 'DNS Server\n\n• Subdomain-Management\n• Service Discovery\n• Load Balancing Records',
                    'proxy': 'WebProxy\n\n• Reverse Proxy\n• SSL Termination\n• Traffic Routing',
                    'docker': 'Docker Host\n\n• Container Runtime\n• Service Orchestrierung\n• Resource Management'
                };
                alert(serverDescriptions[serverType] || `${this.querySelector('.server-title').textContent}\n\n${this.querySelector('.server-desc').textContent}`);
            });
        });
        
        // Click handlers for stages
        document.querySelectorAll('.stage').forEach(stage => {
            stage.addEventListener('click', function() {
                const stageNum = this.dataset.stage;
                const actions = Array.from(this.querySelectorAll('.stage-actions li')).map(li => li.textContent).join('\n• ');
                alert(`Stage ${stageNum} Details:\n\n• ${actions}`);
            });
        });
    </script>
</body>
</html>