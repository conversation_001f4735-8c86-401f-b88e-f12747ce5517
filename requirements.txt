# LEOS360 Platform - Python Requirements
# =====================================
# 
# This file contains all Python package dependencies required for the
# LEOS360 tenant setup scripts (stage1-4 and setup_tenant_complete.py)
#
# Installation:
#   pip install -r requirements.txt
#
# Python Version: 3.12+

# HTTP Requests and API Communication
# Used by: stage2_tenant_dns.py, stage2_tenant_webproxy.py, stage3_tenant_docker.py,
#          stage4_tenant_keycloak.py, stage4_tenant_lldap.py, stage4_tenant_portal.py
requests>=2.31.0

# PostgreSQL Database Connectivity
# Used by: stage2_tenant_db.py
psycopg2-binary>=2.9.7

# Environment Variable Management
# Used by: stage2_tenant_db.py, stage2_tenant_dns.py, stage3_tenant_docker.py,
#          stage4_tenant_keycloak.py, stage4_tenant_lldap.py, stage4_tenant_portal.py
python-dotenv>=1.0.0

# HTTP Client Library (for SSL handling)
# Used by: stage3_tenant_docker.py, stage4_tenant_lldap.py
urllib3>=2.0.0

# Optional: For better SSL certificate handling
# Uncomment if you encounter SSL certificate issues
# certifi>=2023.7.22

# Development Dependencies (optional)
# Uncomment for development/testing
# pytest>=7.4.0
# pytest-cov>=4.1.0
# black>=23.7.0
# flake8>=6.0.0
