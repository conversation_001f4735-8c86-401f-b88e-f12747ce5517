#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 2: DNS Configuration
=============================================

This script handles the DNS configuration for a new tenant in the LEOS360 platform.
It creates CNAME records for the customer domain, SSO endpoint, and API endpoint
using PowerDNS API.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 must be completed successfully
- PowerDNS API must be accessible
- API credentials must be configured in environment
- DNS zone must exist and be manageable

Usage:
    python3 stage2_tenant_dns.py <customer_name> [options]

Examples:
    python3 stage2_tenant_dns.py example-customer
    python3 stage2_tenant_dns.py example-customer --delete
"""

import os
import sys
import time
import argparse
from typing import Tuple, List, Optional
import requests
from dotenv import load_dotenv

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 2: DNS Configuration"

# Load environment variables
load_dotenv()

# DNS configuration
API_URL = os.getenv("API_URL")
API_KEY = os.getenv("API_KEY")
ZONE_NAME = os.getenv("ZONE_NAME")
TARGET_CNAME = "webproxy01.leos360.cloud."

# DNS record configuration
DNS_TTL = 3600
DNS_RECORD_TYPE = "CNAME"

# Validate required environment variables
if not API_URL or not API_KEY or not ZONE_NAME:
    print("[ERROR] API_URL, API_KEY and ZONE_NAME must be set in environment!")
    sys.exit(1)


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


# =============================================================================
# MAIN DNS CONFIGURATION CLASS
# =============================================================================

class TenantDNSSetup:
    """
    Handles DNS configuration for a new tenant in the LEOS360 platform.

    This class manages the complete DNS setup process including:
    - CNAME record creation for customer domain
    - SSO endpoint DNS configuration
    - API endpoint DNS configuration
    - PowerDNS zone management and activation
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant DNS setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValueError: If customer_name is invalid
        """
        self.customer_name = customer_name.strip()
        print_step(f"Initializing DNS setup for: {self.customer_name}")

        # DNS record names for this customer
        self.dns_records = [
            f"{self.customer_name}.leos360.cloud.",
            f"{self.customer_name}-sso.leos360.cloud.",
            f"{self.customer_name}-api.leos360.cloud."
        ]

        # API configuration
        self.api_url = API_URL
        self.api_key = API_KEY
        self.zone_name = ZONE_NAME
        self.target_cname = TARGET_CNAME

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def _make_api_request(self, method: str, url: str, data: dict = None) -> Tuple[bool, Optional[dict]]:
        """
        Make an API request to PowerDNS.

        Args:
            method: HTTP method (GET, PATCH, PUT)
            url: API endpoint URL
            data: Optional request data

        Returns:
            Tuple of (success, response_data)
        """
        headers = {
            "X-API-Key": self.api_key,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            elif method.upper() == "PATCH":
                response = requests.patch(url, json=data, headers=headers)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=headers)
            else:
                print_error(f"Unsupported HTTP method: {method}")
                return False, None

            response.raise_for_status()
            return True, response.json() if response.content else None

        except requests.exceptions.RequestException as e:
            print_error(f"API request failed: {e}")
            return False, None

    def check_dns_record_exists(self, record_name: str, record_type: str) -> Tuple[bool, Optional[str]]:
        """
        Check if a DNS record already exists.

        Args:
            record_name: DNS record name
            record_type: DNS record type (e.g., CNAME)

        Returns:
            Tuple of (exists, content)
        """
        url = f"{self.api_url}/zones/{self.zone_name}"
        success, zone_data = self._make_api_request("GET", url)

        if not success or not zone_data:
            return False, None

        # Search for the specific record
        if "rrsets" in zone_data:
            for rrset in zone_data["rrsets"]:
                if rrset["name"] == record_name and rrset["type"] == record_type:
                    for record in rrset["records"]:
                        if not record["disabled"]:
                            print_step(f"DNS record {record_name} ({record_type}) already exists: {record['content']}")
                            return True, record["content"]

        return False, None

    # =========================================================================
    # DNS RECORD MANAGEMENT METHODS
    # =========================================================================

    def update_dns_record(self, record_name: str, record_type: str, record_content: str) -> Tuple[bool, bool]:
        """
        Create or update a DNS record.

        Args:
            record_name: DNS record name
            record_type: DNS record type
            record_content: DNS record content

        Returns:
            Tuple of (success, changed)
        """
        # Remove spaces from record name
        record_name = record_name.replace(" ", "")

        # Check if record already exists
        exists, existing_content = self.check_dns_record_exists(record_name, record_type)
        if exists:
            # If record exists with same content, no change needed
            if existing_content == record_content:
                print_step(f"DNS record {record_name} already exists with correct content. No change needed.")
                return True, False
            # If record exists with different content, update it
            print_step(f"DNS record {record_name} exists with different content: {existing_content}")
            print_step(f"Updating to: {record_content}")

        url = f"{self.api_url}/zones/{self.zone_name}"

        # Create or update DNS data
        data = {
            "rrsets": [
                {
                    "name": record_name,
                    "type": record_type,
                    "ttl": DNS_TTL,
                    "changetype": "REPLACE",
                    "records": [
                        {"content": record_content, "disabled": False}
                    ]
                }
            ]
        }

        action = "update" if exists else "create"
        print_step(f"Attempting to {action} DNS record for {record_name}...")

        success, _ = self._make_api_request("PATCH", url, data)
        if success:
            print_success(f"DNS record for {record_name} was successfully {'updated' if exists else 'created'}!")
            return True, True
        else:
            return False, False

    def delete_dns_record(self, record_name: str, record_type: str) -> Tuple[bool, bool]:
        """
        Delete a DNS record.

        Args:
            record_name: DNS record name
            record_type: DNS record type

        Returns:
            Tuple of (success, changed)
        """
        # Remove spaces from record name
        record_name = record_name.replace(" ", "")

        # Check if record exists
        exists, _ = self.check_dns_record_exists(record_name, record_type)
        if not exists:
            print_step(f"DNS record {record_name} ({record_type}) does not exist. Nothing to delete.")
            return True, False

        url = f"{self.api_url}/zones/{self.zone_name}"

        # DNS data for deletion
        data = {
            "rrsets": [
                {
                    "name": record_name,
                    "type": record_type,
                    "changetype": "DELETE",
                    "records": []
                }
            ]
        }

        print_step(f"Attempting to delete DNS record {record_name} ({record_type})...")

        success, _ = self._make_api_request("PATCH", url, data)
        if success:
            print_success(f"DNS record {record_name} ({record_type}) was successfully deleted!")
            return True, True
        else:
            return False, False

    # =========================================================================
    # ZONE MANAGEMENT METHODS
    # =========================================================================

    def notify_zone(self) -> bool:
        """
        Send a NOTIFY request for the zone.

        Returns:
            True if successful, False otherwise
        """
        url = f"{self.api_url}/zones/{self.zone_name}/notify"
        print_step(f"Sending NOTIFY for zone {self.zone_name}...")

        success, _ = self._make_api_request("PUT", url)
        if success:
            print_success(f"NOTIFY for zone {self.zone_name} sent successfully!")
            return True
        else:
            return False

    def rectify_zone(self) -> bool:
        """
        Rectify the zone (corrects DNSSEC records).

        Returns:
            True if successful, False otherwise
        """
        url = f"{self.api_url}/zones/{self.zone_name}/rectify"
        print_step(f"Performing rectify for zone {self.zone_name}...")

        success, _ = self._make_api_request("PUT", url)
        if success:
            print_success(f"Rectify for zone {self.zone_name} completed successfully!")
            return True
        else:
            return False

    def activate_changes(self) -> List[str]:
        """
        Activate changes by rectifying and notifying the zone.

        Returns:
            List of successful actions
        """
        success_actions = []

        # 1. Rectify Zone
        if self.rectify_zone():
            success_actions.append("rectify")

        # 2. Notify
        if self.notify_zone():
            success_actions.append("notify")

        # Short pause for potential asynchronous processing
        time.sleep(1)

        return success_actions

    # =========================================================================
    # MAIN OPERATION METHODS
    # =========================================================================

    def setup_dns_records(self) -> bool:
        """
        Set up DNS records for this customer.

        Returns:
            True if all operations successful, False otherwise
        """
        print_step(f"Setting up DNS records for customer: {self.customer_name}")

        all_successful = True
        any_changes = False

        # Create/update all DNS records
        for record in self.dns_records:
            success, changed = self.update_dns_record(record, DNS_RECORD_TYPE, self.target_cname)
            all_successful = all_successful and success
            any_changes = any_changes or changed

        if all_successful:
            if any_changes:
                success_actions = self.activate_changes()
                if "rectify" in success_actions and "notify" in success_actions:
                    print_success(f"DNS records for {self.customer_name} successfully created and activated!")
                    print_success(f"Successful actions: {', '.join(success_actions)}")
                    return True
                elif success_actions:
                    print_warning(f"DNS records created, but only partially activated: {', '.join(success_actions)}")
                    return False
                else:
                    print_error("DNS records were created, but there were problems activating them.")
                    return False
            else:
                print_success(f"All DNS records for {self.customer_name} already in desired state.")
                print_success("No changes made, no reload necessary.")
                return True
        else:
            print_error("Not all DNS records could be created.")
            return False

    def delete_dns_records(self) -> bool:
        """
        Delete DNS records for this customer.

        Returns:
            True if all operations successful, False otherwise
        """
        print_step(f"Deleting DNS records for customer: {self.customer_name}")

        all_successful = True
        any_changes = False

        # Delete all DNS records
        for record in self.dns_records:
            success, changed = self.delete_dns_record(record, DNS_RECORD_TYPE)
            all_successful = all_successful and success
            any_changes = any_changes or changed

        if all_successful:
            if any_changes:
                success_actions = self.activate_changes()
                if "rectify" in success_actions and "notify" in success_actions:
                    print_success(f"DNS records for {self.customer_name} successfully deleted and activated!")
                    print_success(f"Successful actions: {', '.join(success_actions)}")
                    return True
                elif success_actions:
                    print_warning(f"DNS records deleted, but only partially activated: {', '.join(success_actions)}")
                    return False
                else:
                    print_error("DNS records were deleted, but there were problems activating them.")
                    return False
            else:
                print_success(f"All DNS records for {self.customer_name} already in desired state.")
                print_success("No changes made, no reload necessary.")
                return True
        else:
            print_error("Not all DNS records could be deleted.")
            return False

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str) -> bool:
        """
        Main execution function that orchestrates the DNS setup process.

        Args:
            operation: Operation to perform ('setup' or 'delete')

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 DNS Configuration - {self.customer_name}")
        print_step(f"Starting DNS {operation} for {self.customer_name}...")

        try:
            if operation == "delete":
                success = self.delete_dns_records()
            else:  # default: setup
                success = self.setup_dns_records()

            if success:
                print_header("OPERATION COMPLETED SUCCESSFULLY")
                print_success(f"DNS {operation} for {self.customer_name} completed successfully!")
                return True
            else:
                print_error(f"DNS {operation} failed for {self.customer_name}")
                return False

        except Exception as e:
            print_error(f"DNS {operation} failed: {str(e)}")
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute DNS setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup tenant DNS for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage2_tenant_dns.py example-customer
  python3 stage2_tenant_dns.py example-customer --delete

Requirements:
  - Stage 1 must be completed successfully
  - PowerDNS API must be accessible
  - API credentials must be configured in environment
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    parser.add_argument(
        '--delete',
        action='store_true',
        help='Delete DNS records instead of creating them'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    operation = "delete" if args.delete else "setup"

    try:
        # Create and run DNS setup
        setup = TenantDNSSetup(args.customer_name)
        success = setup.run(operation)

        if not success:
            sys.exit(1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("DNS setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()