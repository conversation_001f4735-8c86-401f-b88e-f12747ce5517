#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 4: Keycloak Setup
===========================================

This script handles the Keycloak setup for a new tenant in the LEOS360 platform.
It creates realms, clients, LDAP federation, and configures authentication flows
for the complete identity management infrastructure.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Stage 1, 2, and 3 must be completed successfully
- Keycloak server must be accessible
- LLDAP container must be running and configured
- Environment variables must be properly configured

Usage:
    python3 stage4_tenant_keycloak.py <customer_name> [options]

Example:
    python3 stage4_tenant_keycloak.py example-customer
    python3 stage4_tenant_keycloak.py example-customer --status
"""

import os
import sys
import requests
import urllib3
import time
import argparse
from pathlib import Path
from typing import Dict, Optional, Any
from dotenv import load_dotenv

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 4: Keycloak Setup"

# File paths
BASE_TENANT_PATH = Path("/mnt/storage/tenants")

# Keycloak configuration
DEFAULT_VERIFY_SSL = True
API_TIMEOUT = 30

# Global variables for legacy function compatibility
CUSTOMER_NAME = None
CUSTOMER_DOMAIN = None
BASE_DOMAIN = None
KC_HOSTNAME = None
KEYCLOAK_ADMIN_USERNAME = None
KEYCLOAK_ADMIN_PASSWORD = None
KEYCLOAK_REALM = None
VERIFY_SSL = True


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


# =============================================================================
# LEGACY FUNCTIONS (TO BE REFACTORED)
# =============================================================================

def load_env_file(customer_name):
    """Lädt die .env-Datei aus dem angegebenen Verzeichnis für den Kundennamen"""
    global CUSTOMER_NAME, CUSTOMER_DOMAIN, BASE_DOMAIN, KC_HOSTNAME, KEYCLOAK_ADMIN_USERNAME
    global KEYCLOAK_ADMIN_PASSWORD, KEYCLOAK_REALM, VERIFY_SSL
    
    env_path = f"/mnt/storage/tenants/{customer_name}/.env"
    
    if not os.path.exists(env_path):
        print(f"Fehler: .env-Datei konnte nicht gefunden werden unter: {env_path}")
        sys.exit(1)
    
    print(f"Lade Konfiguration aus: {env_path}")
    load_dotenv(env_path)
    
    # Konfigurationsvariablen laden
    CUSTOMER_NAME = os.getenv("CUSTOMER_NAME")
    CUSTOMER_DOMAIN = os.getenv("CUSTOMER_DOMAIN")
    BASE_DOMAIN = os.getenv("BASE_DOMAIN")
    KC_HOSTNAME = os.getenv("KC_HOSTNAME", "").rstrip('/')
    KEYCLOAK_ADMIN_USERNAME = os.getenv("KEYCLOAK_ADMIN_USERNAME")
    KEYCLOAK_ADMIN_PASSWORD = os.getenv("KEYCLOAK_ADMIN_PASSWORD")
    KEYCLOAK_REALM = os.getenv("KEYCLOAK_REALM")
    
    return env_path

def get_keycloak_base_url():
    """Basis-URL für Keycloak API ermitteln"""
    if KC_HOSTNAME.startswith("https://"):
        return KC_HOSTNAME
    else:
        return f"https://{KC_HOSTNAME}"

def get_keycloak_token():
    """Abrufen eines Access Tokens von Keycloak"""
    base_url = get_keycloak_base_url()
    url = f"{base_url}/realms/master/protocol/openid-connect/token"
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    data = {
        "client_id": "admin-cli",
        "username": KEYCLOAK_ADMIN_USERNAME,
        "password": KEYCLOAK_ADMIN_PASSWORD,
        "grant_type": "password"
    }
    
    try:
        response = requests.post(url, headers=headers, data=data, verify=VERIFY_SSL)
        response.raise_for_status()
        token = response.json().get("access_token")
        print("Token erfolgreich abgerufen!")
        
        return token
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Abrufen des Access Tokens: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        exit(1)

def check_realm_exists(token, realm_name):
    """Überprüfen, ob der Realm bereits existiert"""
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:        
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        
        if response.status_code == 200:
            print(f"Realm '{realm_name}' existiert!")
            return True
        elif response.status_code == 404:
            print(f"Realm '{realm_name}' existiert nicht!")
            return False
        else:
            print(f"Unerwarteter Status Code: {response.status_code}")
            response.raise_for_status()
            
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Überprüfen des Realms: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return False

def create_realm(token, realm_name):
    """Erstellen eines minimalen Realms mit nur den notwendigsten Einstellungen"""
    if check_realm_exists(token, realm_name):
        return realm_name
    
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
        
    # Minimale Konfiguration
    realm_data = {
        "realm": realm_name,
        "enabled": True
    }
    
    try:
        response = requests.post(url, headers=headers, json=realm_data, verify=VERIFY_SSL)
        response.raise_for_status()
        print(f"Realm '{realm_name}' erfolgreich erstellt!")
        
        time.sleep(2)
        
        if check_realm_exists(token, realm_name):
            print(f"Realm '{realm_name}' wurde erfolgreich erstellt und ist verfügbar!")
        else:
            print(f"Warnung: Realm '{realm_name}' wurde erstellt, aber scheint noch nicht verfügbar zu sein!")
        
        return realm_name
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Erstellen des minimalen Realms: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        print("Überprüfe deine Berechtigungen und die Konfiguration des Keycloak-Servers.")
        return None

def create_client_nextcloud(token, realm_name, customer_domain):
    """
    Erstellt einen Nextcloud-Client mit den spezifischen Einstellungen aus dem JSON-Export
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        customer_domain (str): Die Kundendomain (z.B. testkunde6.leos360.cloud)
    
    Returns:
        str: Die ID des erstellten Clients oder None bei Fehlern
    """
    # Prüfen, ob der Client bereits existiert
    if check_client_exists(token, realm_name, "nextcloud"):
        print("Nextcloud Client existiert bereits!")
        # Client-ID des bestehenden Clients zurückgeben
        clients = get_realm_clients(token, realm_name)
        for client in clients:
            if client.get('clientId') == "nextcloud":
                return client.get('id')
        return None
    
    # Client-Secret aus Umgebungsvariable laden
    client_secret = os.getenv("NEXTCLOUD_KEYCLOAK_CLIENT_SECRET")

    # Aktuelle Zeit für client.secret.creation.time
    current_time = int(time.time())
    
    # Client-Konfiguration erstellen basierend auf dem JSON-Export
    client_data = {
        "clientId": "nextcloud",
        "name": "nextcloud",
        "description": "",
        "rootUrl": f"https://{customer_domain}",
        "adminUrl": f"https://{customer_domain}",
        "baseUrl": f"https://{customer_domain}",
        "surrogateAuthRequired": False,
        "enabled": True,
        "alwaysDisplayInConsole": False,
        "clientAuthenticatorType": "client-secret",
        "secret": client_secret,
        "redirectUris": [
            f"https://{customer_domain}/*"
        ],
        "webOrigins": [
            f"https://{customer_domain}"
        ],
        "notBefore": 0,
        "bearerOnly": False,
        "consentRequired": False,
        "standardFlowEnabled": True,
        "implicitFlowEnabled": False,
        "directAccessGrantsEnabled": False,
        "serviceAccountsEnabled": False,
        "publicClient": False,
        "frontchannelLogout": False,
        "protocol": "openid-connect",
        "attributes": {
            "realm_client": "false",
            "oidc.ciba.grant.enabled": "false",
            "client.secret.creation.time": str(current_time),
            "backchannel.logout.session.required": "false",
            "standard.token.exchange.enabled": "false",
            "frontchannel.logout.session.required": "true",
            "oauth2.device.authorization.grant.enabled": "false",
            "display.on.consent.screen": "false",
            "backchannel.logout.revoke.offline.tokens": "false"
        },
        "authenticationFlowBindingOverrides": {},
        "fullScopeAllowed": False,
        "nodeReRegistrationTimeout": -1,
        "protocolMappers": [
            {
                "name": "client roles",
                "protocol": "openid-connect",
                "protocolMapper": "oidc-usermodel-client-role-mapper",
                "consentRequired": False,
                "config": {
                    "introspection.token.claim": "true",
                    "multivalued": "true",
                    "userinfo.token.claim": "true",
                    "user.attribute": "foo",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "access.token.claim": "true",
                    "claim.name": "resource_access.${client_id}.roles",
                    "jsonType.label": "String"
                }
            },
            {
                "name": "aud-nextcloud",
                "protocol": "openid-connect",
                "protocolMapper": "oidc-audience-mapper",
                "consentRequired": False,
                "config": {
                    "included.client.audience": "nextcloud",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "introspection.token.claim": "true",
                    "access.token.claim": "true",
                    "userinfo.token.claim": "false"
                }
            }
        ],
        "defaultClientScopes": [
            "web-origins",
            "acr",
            "profile",
            "roles",
            "basic",
            "email"
        ],
        "optionalClientScopes": [
            "address",
            "phone",
            "organization",
            "offline_access",
            "microprofile-jwt"
        ]
    }
    
    # Client erstellen
    print(f"Erstelle Nextcloud-Client für Domain: {customer_domain}")
    client_id = create_client(token, realm_name, client_data)
    
    if client_id:
        print(f"Nextcloud-Client erfolgreich erstellt!")
    else:
        print("Fehler beim Erstellen des Nextcloud-Clients!")
    
    return client_id

def create_client_portal(token, realm_name, customer_domain):
    """
    Erstellt einen leos360portal Client mit den spezifischen Einstellungen
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        customer_domain (str): Die Kundendomain (z.B. testkunde6.leos360.cloud)
    
    Returns:
        str: Die ID des erstellten Clients oder None bei Fehlern
    """
    # Prüfen, ob der Client bereits existiert
    if check_client_exists(token, realm_name, "leos360portal"):
        print("LEOS360 Portal Client existiert bereits!")
        # Client-ID des bestehenden Clients zurückgeben
        clients = get_realm_clients(token, realm_name)
        for client in clients:
            if client.get('clientId') == "leos360portal":
                return client.get('id')
        return None
    
    # Client-Secret aus Umgebungsvariable laden
    client_secret = os.getenv("LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET")
    leos360sso_url = os.getenv("leos360sso_url")
    
    # Aktuelle Zeit für client.secret.creation.time
    current_time = int(time.time())
    
    # Client-Konfiguration erstellen
    client_data = {
        "clientId": "leos360portal",
        "name": "leos360portal",
        "description": "",
        "rootUrl": f"{leos360sso_url}",
        "adminUrl": f"{leos360sso_url}",
        "baseUrl": f"{leos360sso_url}",
        "surrogateAuthRequired": False,
        "enabled": True,
        "alwaysDisplayInConsole": False,
        "clientAuthenticatorType": "client-secret",
        "secret": client_secret,
        "redirectUris": [
            f"{leos360sso_url}/*"
        ],
        "webOrigins": [
            f"{leos360sso_url}"
        ],
        "notBefore": 0,
        "bearerOnly": False,
        "consentRequired": False,
        "standardFlowEnabled": True,
        "implicitFlowEnabled": False,
        "directAccessGrantsEnabled": False,
        "serviceAccountsEnabled": False,
        "publicClient": False,
        "frontchannelLogout": False,
        "protocol": "openid-connect",
        "attributes": {
            "realm_client": "false",
            "oidc.ciba.grant.enabled": "false",
            "client.secret.creation.time": str(current_time),
            "backchannel.logout.session.required": "false",
            "standard.token.exchange.enabled": "false",
            "frontchannel.logout.session.required": "true",
            "oauth2.device.authorization.grant.enabled": "false",
            "display.on.consent.screen": "false",
            "backchannel.logout.revoke.offline.tokens": "false"
        },
        "authenticationFlowBindingOverrides": {},
        "fullScopeAllowed": False,
        "nodeReRegistrationTimeout": -1,
        "protocolMappers": [
            {
                "name": "client roles",
                "protocol": "openid-connect",
                "protocolMapper": "oidc-usermodel-client-role-mapper",
                "consentRequired": False,
                "config": {
                    "introspection.token.claim": "true",
                    "multivalued": "true",
                    "userinfo.token.claim": "true",
                    "user.attribute": "foo",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "access.token.claim": "true",
                    "claim.name": "resource_access.${client_id}.roles",
                    "jsonType.label": "String"
                }
            },
            {
                "name": "aud-leos360portal",
                "protocol": "openid-connect",
                "protocolMapper": "oidc-audience-mapper",
                "consentRequired": False,
                "config": {
                    "included.client.audience": "leos360portal",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "introspection.token.claim": "true",
                    "access.token.claim": "true",
                    "userinfo.token.claim": "false"
                }
            }
        ],
        "defaultClientScopes": [
            "web-origins",
            "acr",
            "profile",
            "roles",
            "basic",
            "email"
        ],
        "optionalClientScopes": [
            "address",
            "phone",
            "organization",
            "offline_access",
            "microprofile-jwt"
        ]
    }
    
    # Client erstellen
    print(f"Erstelle LEOS360 Portal Client für Domain: {customer_domain}")
    client_id = create_client(token, realm_name, client_data)
    
    if client_id:
        print(f"LEOS360 Portal Client erfolgreich erstellt!")
    else:
        print("Fehler beim Erstellen des LEOS360 Portal Clients!")
    
    return client_id

def get_realm_clients(token, realm_name, verbose=False):
    """Abrufen aller Clients im angegebenen Realm"""
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/clients"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if verbose:
            print(f"Rufe Clients im Realm '{realm_name}' ab...")
        
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        clients = response.json()
        
        if verbose:
            print(f"{len(clients)} Clients gefunden!")
        
        return clients
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Abrufen der Clients: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return []

def check_client_exists(token, realm_name, client_id, verbose=False):
    """Überprüfen, ob ein Client mit der angegebenen clientId existiert"""
    clients = get_realm_clients(token, realm_name, verbose=verbose)
    
    for client in clients:
        if client.get('clientId') == client_id:
            if verbose:
                print(f"Client mit clientId '{client_id}' existiert!")
            return True
    
    if verbose:
        print(f"Client mit clientId '{client_id}' existiert nicht!")
    return False

def create_client(token, realm_name, client_data):
    """Erstellen eines neuen Clients im angegebenen Realm"""
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/clients"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"Erstelle Client '{client_data.get('clientId')}' im Realm '{realm_name}'...")
    
    try:
        response = requests.post(url, headers=headers, json=client_data, verify=VERIFY_SSL)
        response.raise_for_status()
        print(f"Client '{client_data.get('clientId')}' erfolgreich erstellt!")
        
        # Abrufen der Client-ID vom erstellten Client
        clients = get_realm_clients(token, realm_name)
        client_id = None
        for client in clients:
            if client.get('clientId') == client_data.get('clientId'):
                client_id = client.get('id')
                break
        
        return client_id
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Erstellen des Clients: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return None

def get_realm_id(token, realm_name):
    """Abrufen der ID eines Realms anhand seines Namens"""
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        realm_data = response.json()
        realm_id = realm_data.get('id')
        
        return realm_id
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Abrufen der Realm-ID: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return None

def create_ldap_federation(token, realm_name, base_dn, customer_domain, customer_ip, ldap_ro_pass):
    """
    Erstellt eine LDAP User Federation im angegebenen Realm
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        base_dn (str): Die Base DN des LDAP-Servers (z.B. dc=testkunde6,dc=leos360,dc=cloud)
        customer_domain (str): Die Kundendomain (z.B. testkunde6.leos360.cloud)
        customer_ip (str): Die IP-Adresse des Kunden (z.B. ***********)
        ldap_ro_pass (str): Das Passwort für den Read-Only LDAP-Benutzer
    
    Returns:
        str: Die ID der erstellten User Federation oder None bei Fehlern
    """
    # Realm-ID abrufen (wichtig für die parentId)
    realm_id = get_realm_id(token, realm_name)
    
    if not realm_id:
        print(f"Fehler: Konnte keine ID für Realm '{realm_name}' abrufen!")
        return None
    
    # Prüfen, ob bereits eine LDAP User Federation existiert
    if check_ldap_federation_exists(token, realm_name):
        print("LDAP User Federation existiert bereits!")
        return get_ldap_federation_id(token, realm_name)
    
    # LDAP-Server-Adresse erstellen (Port 3890)
    ldap_server = f"ldap://{customer_ip}:3890"
    
    # Bind DN erstellen
    bind_dn = f"uid=keycloak_ro,ou=people,{base_dn}"
    
    # Users DN erstellen
    users_dn = f"ou=people,{base_dn}"
    
    # Aktuelle Zeit für lastSync
    current_time = int(time.time())
    
    # LDAP User Federation Konfiguration erstellen
    federation_data = {
        "name": "ldap",
        "providerId": "ldap",
        "providerType": "org.keycloak.storage.UserStorageProvider",
        "parentId": realm_id,  # Hier verwenden wir die tatsächliche Realm-ID
        "config": {
            "fullSyncPeriod": ["3600"],
            "pagination": ["false"],
            "connectionTrace": ["false"],
            "startTls": ["false"],
            "usersDn": [users_dn],
            "connectionPooling": ["true"],
            "cachePolicy": ["DEFAULT"],
            "useKerberosForPasswordAuthentication": ["false"],
            "importEnabled": ["true"],
            "enabled": ["true"],
            "usernameLDAPAttribute": ["uid"],
            "bindDn": [bind_dn],
            "bindCredential": [ldap_ro_pass],
            "changedSyncPeriod": ["3600"],
            "vendor": ["other"],
            "uuidLDAPAttribute": ["uid"],
            "allowKerberosAuthentication": ["false"],
            "connectionUrl": [ldap_server],
            "syncRegistrations": ["true"],
            "authType": ["simple"],
            "krbPrincipalAttribute": ["krb5PrincipalName"],
            "searchScope": ["2"],
            "useTruststoreSpi": ["always"],
            "usePasswordModifyExtendedOp": ["false"],
            "trustEmail": ["false"],
            "userObjectClasses": ["person"],
            "removeInvalidUsersEnabled": ["true"],
            "rdnLDAPAttribute": ["uid"],
            "editMode": ["READ_ONLY"],
            "validatePasswordPolicy": ["false"]
        }
    }
    
    # User Federation erstellen
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/components"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers, json=federation_data, verify=VERIFY_SSL)
        response.raise_for_status()
        
        # Response-ID extrahieren (falls vorhanden)
        federation_id = None
        try:
            location = response.headers.get('Location')
            if location:
                federation_id = location.split('/')[-1]
            else:
                response_data = response.json()
                federation_id = response_data.get('id')
        except:
            pass
        
        if federation_id:
            return federation_id
        
        print(f"LDAP User Federation erfolgreich erstellt!")
        
        # ID der erstellten User Federation abrufen
        federation_id = get_ldap_federation_id(token, realm_name)
        if federation_id:
            print(f"LDAP User Federation ID: {federation_id}")
        else:
            print(f"LDAP User Federation wurde erstellt, konnte aber nicht gefunden werden.")
        
        return federation_id
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Erstellen der LDAP User Federation: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
            
            # Detaillierte Fehlermeldung ausgeben
            try:
                error_details = e.response.json()
                print(f"Fehlerdetails: {error_details}")
            except:
                pass
                
        return None

def check_ldap_federation_exists(token, realm_name, verbose=False):
    """Überprüfen, ob bereits eine LDAP User Federation im Realm existiert"""
    federations = get_realm_user_federations(token, realm_name, verbose=verbose)
    
    for federation in federations:
        if federation.get('providerId') == 'ldap':
            if verbose:
                print("LDAP User Federation existiert bereits!")
            return True
    
    if verbose:
        print("Keine LDAP User Federation gefunden!")
    return False

def get_ldap_federation_id(token, realm_name):
    """Abrufen der ID der LDAP User Federation im Realm"""
    federations = get_realm_user_federations(token, realm_name)
    
    for federation in federations:
        if federation.get('providerId') == 'ldap':
            return federation.get('id')
    
    return None

def get_realm_user_federations(token, realm_name, verbose=False):
    """Abrufen aller User Federations im angegebenen Realm"""
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/components?type=org.keycloak.storage.UserStorageProvider"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if verbose:
            print(f"Rufe User Federations im Realm '{realm_name}' ab...")
        
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        providers = response.json()
        
        if verbose:
            print(f"{len(providers)} User Federations gefunden!")
        
        return providers
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Abrufen der User Federations: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return []

def create_ldap_mapper_group(token, realm_name, federation_id, base_dn, customer_domain):
    """
    Erstellt einen LDAP Group Mapper für eine bestehende LDAP User Federation
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        federation_id (str): Die ID der LDAP User Federation
        base_dn (str): Die Base DN des LDAP-Servers (z.B. dc=testkunde6,dc=leos360,dc=cloud)
        customer_domain (str): Die Kundendomain (z.B. testkunde6.leos360.cloud)
    
    Returns:
        str: Die ID des erstellten Mappers oder None bei Fehlern
    """
    # Name des Mappers
    mapper_name = "lldap-groups"
    
    # Prüfen, ob der Mapper bereits existiert
    if check_ldap_mapper_exists(token, realm_name, federation_id, mapper_name):
        print(f"LDAP Group Mapper '{mapper_name}' existiert bereits!")
        return get_ldap_mapper_id(token, realm_name, federation_id, mapper_name)
    
    # Groups DN erstellen
    groups_dn = f"ou=groups,{base_dn}"
    
    # Mapper-Konfiguration erstellen
    mapper_data = {
        "name": mapper_name,
        "providerId": "group-ldap-mapper",
        "providerType": "org.keycloak.storage.ldap.mappers.LDAPStorageMapper",
        "parentId": federation_id,
        "config": {
            "mode": ["LDAP_ONLY"],
            "membership.attribute.type": ["DN"],
            "user.roles.retrieve.strategy": ["LOAD_GROUPS_BY_MEMBER_ATTRIBUTE"],
            "group.name.ldap.attribute": ["cn"],
            "membership.ldap.attribute": ["member"],
            "membership.user.ldap.attribute": ["uid"],
            "ignore.missing.groups": ["false"],
            "preserve.group.inheritance": ["false"],
            "memberof.ldap.attribute": ["memberOf"],
            "group.object.classes": ["groupOfNames"],
            "groups.dn": [groups_dn],
            "drop.non.existing.groups.during.sync": ["false"],
            "groups.path": ["/"]
        }
    }
    
    # Mapper erstellen
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/components"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers, json=mapper_data, verify=VERIFY_SSL)
        response.raise_for_status()
        
        # Response-ID extrahieren (falls vorhanden)
        mapper_id = None
        try:
            location = response.headers.get('Location')
            if location:
                mapper_id = location.split('/')[-1]
            else:
                response_data = response.json()
                mapper_id = response_data.get('id')
        except:
            pass
        
        if mapper_id:
            return mapper_id
        
        print(f"LDAP Group Mapper '{mapper_name}' erfolgreich erstellt!")
        
        # ID des erstellten Mappers abrufen
        mapper_id = get_ldap_mapper_id(token, realm_name, federation_id, mapper_name)
        if mapper_id:
            print(f"Mapper ID: {mapper_id}")
        else:
            print(f"Mapper wurde erstellt, konnte aber nicht gefunden werden.")
        
        return mapper_id
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Erstellen des LDAP Group Mappers: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
            
            # Detaillierte Fehlermeldung ausgeben
            try:
                error_details = e.response.json()
                print(f"Fehlerdetails: {error_details}")
            except:
                pass
                
        return None

def get_ldap_mappers(token, realm_name, federation_id, verbose=False):
    """Abrufen aller LDAP-Mapper für eine bestimmte LDAP User Federation"""
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/components?parent={federation_id}&type=org.keycloak.storage.ldap.mappers.LDAPStorageMapper"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if verbose:
            print(f"Rufe LDAP-Mapper für Federation '{federation_id}' ab...")
        
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        mappers = response.json()
        
        if verbose:
            print(f"{len(mappers)} LDAP-Mapper gefunden!")
        
        return mappers
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Abrufen der LDAP-Mapper: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return []

def check_ldap_mapper_exists(token, realm_name, federation_id, mapper_name, verbose=False):
    """Überprüfen, ob ein LDAP-Mapper mit dem angegebenen Namen existiert"""
    mappers = get_ldap_mappers(token, realm_name, federation_id, verbose=verbose)
    
    for mapper in mappers:
        if mapper.get('name') == mapper_name:
            if verbose:
                print(f"LDAP-Mapper mit Namen '{mapper_name}' existiert!")
            return True
    
    if verbose:
        print(f"LDAP-Mapper mit Namen '{mapper_name}' existiert nicht!")
    return False

def get_ldap_mapper_id(token, realm_name, federation_id, mapper_name):
    """Abrufen der ID eines LDAP-Mappers anhand seines Namens"""
    mappers = get_ldap_mappers(token, realm_name, federation_id)
    
    for mapper in mappers:
        if mapper.get('name') == mapper_name:
            return mapper.get('id')
    
    return None

def update_ldap_mapper(token, realm_name, federation_id, mapper_name, config_updates):
    """
    Aktualisiert einen bestehenden LDAP-Mapper
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        federation_id (str): Die ID der LDAP User Federation
        mapper_name (str): Der Name des zu aktualisierenden Mappers
        config_updates (dict): Die zu aktualisierenden Konfigurationswerte als Dictionary
    
    Returns:
        bool: True bei Erfolg, False bei Fehlern
    """
    # ID des Mappers abrufen
    mapper_id = get_ldap_mapper_id(token, realm_name, federation_id, mapper_name)
    
    if not mapper_id:
        return False
    
    # Aktuelle Mapper-Konfiguration abrufen
    mapper = get_ldap_mapper_by_id(token, realm_name, mapper_id)
    
    if not mapper:
        return False
    
    # Konfiguration aktualisieren
    current_config = mapper.get('config', {})
    
    for key, value in config_updates.items():
        if isinstance(value, list):
            current_config[key] = value
        else:
            current_config[key] = [value]
    
    mapper['config'] = current_config
    
    # Mapper aktualisieren
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/components/{mapper_id}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.put(url, headers=headers, json=mapper, verify=VERIFY_SSL)
        response.raise_for_status()
        print(f"LDAP-Mapper '{mapper_name}' erfolgreich aktualisiert!")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Aktualisieren des LDAP-Mappers: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return False

def get_ldap_mapper_by_id(token, realm_name, mapper_id):
    """Abrufen eines bestimmten LDAP-Mappers anhand seiner ID"""
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/components/{mapper_id}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
       
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        mapper = response.json()
        
        print(f"LDAP-Mapper '{mapper.get('name')}' gefunden!")
        return mapper
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Abrufen des LDAP-Mappers: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return None

def sync_ldap_users(token, realm_name, federation_id, action="triggerFullSync"):
    """
    Führt eine LDAP-Benutzersynchronisierung durch
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        federation_id (str): Die ID der LDAP User Federation
        action (str): Die Synchronisierungsaktion (triggerFullSync oder triggerChangedUsersSync)
    
    Returns:
        bool: True bei Erfolg, False bei Fehlern
    """
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/user-storage/{federation_id}/{action}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
        
    try:
        response = requests.post(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        print(f"LDAP-Benutzersynchronisierung erfolgreich gestartet!")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Starten der LDAP-Benutzersynchronisierung: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return False

def sync_ldap_groups(token, realm_name, federation_id):
    """
    Führt eine LDAP-Gruppensynchronisierung durch
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        federation_id (str): Die ID der LDAP User Federation
    
    Returns:
        bool: True bei Erfolg, False bei Fehlern
    """
    # Zuerst alle Mapper für die LDAP Federation abrufen
    mappers = get_ldap_mappers(token, realm_name, federation_id)
    
    # Group Mapper suchen
    group_mapper_id = None
    for mapper in mappers:
        if mapper.get('providerId') == 'group-ldap-mapper':
            group_mapper_id = mapper.get('id')
            break
    
    if not group_mapper_id:
        print(f"Fehler: Kein Group Mapper für LDAP Federation '{federation_id}' gefunden!")
        return False
    
    # Allgemeine Federation-Synchronisierung verwenden statt mapper-spezifische
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/user-storage/{federation_id}/sync?action=triggerFullSync"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"Starte LDAP-Synchronisierung für Federation '{federation_id}'...")
    
    try:
        response = requests.post(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        print(f"LDAP-Synchronisierung erfolgreich gestartet!")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Starten der LDAP-Synchronisierung: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return False

def create_client_role(token, realm_name, client_id, role_name, description=None):
    """
    Erstellt eine Client-Role für einen bestimmten Client
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        client_id (str): Die ID des Clients
        role_name (str): Der Name der zu erstellenden Role
        description (str, optional): Die Beschreibung der Role
    
    Returns:
        bool: True bei Erfolg, False bei Fehlern
    """
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/clients/{client_id}/roles"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Role-Daten erstellen
    role_data = {
        "name": role_name,
        "clientRole": True,
        "composite": False
    }
    
    if description:
        role_data["description"] = description
    
    try:
        # Zuerst prüfen, ob die Role bereits existiert
        if check_client_role_exists(token, realm_name, client_id, role_name):
            print(f"Client-Role '{role_name}' existiert bereits.")
            return True
                
        response = requests.post(url, headers=headers, json=role_data, verify=VERIFY_SSL)
        response.raise_for_status()
        print(f"Client-Role '{role_name}' erfolgreich erstellt!")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Erstellen der Client-Role: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return False

def check_client_role_exists(token, realm_name, client_id, role_name):
    """
    Überprüft, ob eine Client-Role bereits existiert
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        client_id (str): Die ID des Clients
        role_name (str): Der Name der zu überprüfenden Role
    
    Returns:
        bool: True wenn die Role existiert, sonst False
    """
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/clients/{client_id}/roles"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        roles = response.json()
        
        for role in roles:
            if role.get('name') == role_name:
                return True
        
        return False
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Überprüfen der Client-Role: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return False

def get_realm_group_id(token, realm_name, group_name):
    """
    Sucht die ID einer Realm-Gruppe anhand des Namens
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        group_name (str): Der Name der Gruppe
    
    Returns:
        str: Die ID der Gruppe oder None, wenn nicht gefunden
    """
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/groups"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        groups = response.json()
        
        for group in groups:
            if group.get('name') == group_name:
                group_id = group.get('id')
                return group_id
        
        return None
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Abrufen der Gruppen: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return None

def check_role_mapping_exists(token, realm_name, client_id, role_name, group_id):
    """
    Überprüft, ob eine Client-Role bereits einer Gruppe zugeordnet ist
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        client_id (str): Die ID des Clients
        role_name (str): Der Name der Role
        group_id (str): Die ID der Gruppe
    
    Returns:
        bool: True wenn das Mapping existiert, sonst False
    """
    base_url = get_keycloak_base_url()
    url = f"{base_url}/admin/realms/{realm_name}/groups/{group_id}/role-mappings/clients/{client_id}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        roles = response.json()
        
        for role in roles:
            if role.get('name') == role_name:
                print(f"Role '{role_name}' ist bereits der Gruppe zugeordnet.")
                return True
        
        return False
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Überprüfen des Rolen-Mappings: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return False

def map_client_role_to_group(token, realm_name, client_id, role_name, group_id):
    """
    Fügt eine Client-Role einer LDAP-Gruppe hinzu, wenn sie nicht bereits zugeordnet ist
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        client_id (str): Die ID des Clients
        role_name (str): Der Name der Role
        group_id (str): Die ID der Gruppe
    
    Returns:
        bool: True bei Erfolg, False bei Fehlern
    """
    # Zuerst prüfen, ob das Mapping bereits existiert
    if check_role_mapping_exists(token, realm_name, client_id, role_name, group_id):
        return True
    
    # Rolen des Clients abrufen, um die Rolen-ID zu finden
    base_url = get_keycloak_base_url()
    roles_url = f"{base_url}/admin/realms/{realm_name}/clients/{client_id}/roles"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(roles_url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        roles = response.json()
        
        # Rolen-ID finden
        role_data = None
        for role in roles:
            if role.get('name') == role_name:
                role_data = role
                break
        
        if not role_data:
            return False
        
        # Role zur Gruppe hinzufügen
        mapping_url = f"{base_url}/admin/realms/{realm_name}/groups/{group_id}/role-mappings/clients/{client_id}"
        
        # Role-Mapping-Daten erstellen
        role_mapping = [role_data]
        
        mapping_response = requests.post(mapping_url, headers=headers, json=role_mapping, verify=VERIFY_SSL)
        mapping_response.raise_for_status()
        print(f"Role '{role_name}' erfolgreich zu Gruppe hinzugefügt!")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Fehler beim Hinzufügen der Role zur Gruppe: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return False

def setup_client_roles_and_mappings(token, realm_name, client_id, client_name, status_dict, user_group=None, admin_group=None):
    """
    Erstellt Rollen für einen Client und mappt sie auf LDAP-Gruppen
    
    Args:
        token (str): Der Keycloak-Admin-Token
        realm_name (str): Der Name des Realms
        client_id (str): Die ID des Clients
        client_name (str): Der Name des Clients (z.B. "nextcloud" oder "leos360portal")
        status_dict (dict): Das Status-Dictionary für Statusverfolgung
        user_group (str, optional): Name der Benutzergruppe aus der Konfiguration
        admin_group (str, optional): Name der Administratorengruppe aus der Konfiguration
        
    Returns:
        None
    """
    print(f"\n{client_name.upper()} Client-Rolen und Gruppen-Mapping:")
    
    # Rolen erstellen
    roles_created = True
    
    # "users" Role erstellen
    if create_client_role(token, realm_name, client_id, "users", f"{client_name} users"):
        print("Role 'users' erfolgreich erstellt oder existiert bereits.")
    else:
        print("Fehler beim Erstellen der 'users' Role!")
        roles_created = False
    
    # "admins" Role erstellen
    if create_client_role(token, realm_name, client_id, "admins", f"{client_name} administrators"):
        print("Role 'admins' erfolgreich erstellt oder existiert bereits.")
    else:
        print("Fehler beim Erstellen der 'admins' Role!")
        roles_created = False
    
    if roles_created:
        status_dict[f"{client_name}_roles_created"] = True
        
        # Falls keine benutzerdefinierten Gruppen angegeben wurden, Standardwerte verwenden
        if user_group is None:
            user_group = f"{client_name}-users"
        if admin_group is None:
            admin_group = f"{client_name}-admins"
            
        # LDAP-Gruppen abrufen
        users_group_id = get_realm_group_id(token, realm_name, user_group)
        admins_group_id = get_realm_group_id(token, realm_name, admin_group)
        
        mapping_success = True
        
        # Rolen zu Gruppen hinzufügen, wenn Gruppen existieren
        if users_group_id:
            if map_client_role_to_group(token, realm_name, client_id, "users", users_group_id):
                print(f"Role 'users' erfolgreich mit Gruppe '{user_group}' gemapped!")
            else:
                print(f"Fehler beim mappen der Role 'users' zur LDAP Gruppe '{user_group}'!")
                mapping_success = False
        else:
            print(f"Gruppe '{user_group}' nicht gefunden! Bitte Gruppen syncen/prüfen.")
            mapping_success = False
        
        if admins_group_id:
            if map_client_role_to_group(token, realm_name, client_id, "admins", admins_group_id):
                print(f"Role 'admins' erfolgreich mit Gruppe '{admin_group}' gemapped!")
            else:
                print(f"Fehler beim mappen der Role 'admins' zur LDAP Gruppe '{admin_group}'!")
                mapping_success = False
        else:
            print(f"Gruppe '{admin_group}' nicht gefunden! Bitte Gruppen syncen/prüfen.")
            mapping_success = False
        
        if mapping_success:
            status_dict[f"{client_name}_role_mapping"] = True
            print(f"{client_name.upper()} Client-Rolen wurden erfolgreich mit LDAP-Gruppen gemapped!")


# =============================================================================
# MAIN CLASS
# =============================================================================

class TenantKeycloakSetup:
    """
    Handles Keycloak setup for a new tenant in the LEOS360 platform.

    This class manages the complete Keycloak setup process including:
    - Realm creation and configuration
    - LDAP federation setup and mapping
    - Client creation (Nextcloud and LEOS360 Portal)
    - Role management and group mapping
    - Authentication flow configuration
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant Keycloak setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValueError: If customer_name is invalid
            FileNotFoundError: If required files are missing
        """
        self.customer_name = customer_name
        print_step(f"Initializing Keycloak setup for: {customer_name}")

        # File paths
        self.customer_dir = BASE_TENANT_PATH / customer_name
        self.env_file = self.customer_dir / ".env"

        # Configuration variables (will be loaded later)
        self.config = {}
        self.verify_ssl = DEFAULT_VERIFY_SSL

        # Status tracking
        self.status = {
            "realm": False,
            "nextcloud": False,
            "leos360portal": False,
            "ldap": False,
            "first_name_mapper": False,
            "group_mapper": False,
            "nextcloud_roles_created": False,
            "nextcloud_role_mapping": False,
            "leos360portal_roles_created": False,
            "leos360portal_role_mapping": False
        }

    def load_configuration(self) -> None:
        """Load configuration from environment file."""
        print_step("Loading configuration from environment file...")

        if not self.env_file.exists():
            print_error(f"Environment file not found: {self.env_file}")
            sys.exit(1)

        print_step(f"Loading configuration from: {self.env_file}")
        load_dotenv(self.env_file)

        # Required environment variables
        required_vars = [
            "KC_HOSTNAME",
            "KEYCLOAK_ADMIN_USERNAME",
            "KEYCLOAK_ADMIN_PASSWORD",
            "KEYCLOAK_REALM",
            "CUSTOMER_DOMAIN",
            "LLDAP_BASE_DN",
            "LLDAP_RO_PASS",
            "CUSTOMER_IP"
        ]

        missing_vars = [var for var in required_vars if not os.getenv(var)]

        if missing_vars:
            print_error("Required environment variables missing:")
            for var in missing_vars:
                print_error(f"  - {var}")
            sys.exit(1)

        # Load configuration
        self.config = {
            'kc_hostname': os.getenv("KC_HOSTNAME", "").rstrip('/'),
            'admin_username': os.getenv("KEYCLOAK_ADMIN_USERNAME"),
            'admin_password': os.getenv("KEYCLOAK_ADMIN_PASSWORD"),
            'realm': os.getenv("KEYCLOAK_REALM"),
            'customer_domain': os.getenv("CUSTOMER_DOMAIN"),
            'base_dn': os.getenv("LLDAP_BASE_DN"),
            'ldap_ro_pass': os.getenv("LLDAP_RO_PASS"),
            'customer_ip': os.getenv("CUSTOMER_IP"),
            'customer_name': os.getenv("CUSTOMER_NAME"),
            'base_domain': os.getenv("BASE_DOMAIN")
        }

        print_success("Configuration loaded successfully")

    def run(self, operation: str = "setup") -> bool:
        """
        Execute the Keycloak setup operation.

        Args:
            operation: The operation to perform ('setup' or 'status')

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 Keycloak Setup - {self.customer_name}")
        print_step(f"Starting Keycloak {operation} for {self.customer_name}...")

        try:
            # Step 1: Load configuration
            self.load_configuration()

            # Step 2: Configure SSL warnings
            if not self.verify_ssl:
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                print_warning("SSL verification is disabled. Only recommended for development environments.")

            # Step 3: Perform requested operation
            if operation == "status":
                return self.show_status()
            else:  # default: setup
                success = self.setup_keycloak()

                if success:
                    print_header("OPERATION COMPLETED SUCCESSFULLY")
                    print_success(f"Keycloak setup for {self.customer_name} completed successfully!")

                return success

        except Exception as e:
            print_error(f"Keycloak {operation} failed: {str(e)}")
            return False

    def setup_keycloak(self) -> bool:
        """Execute the complete Keycloak setup process."""
        print_step("Starting Keycloak setup process...")

        # Load legacy global variables for compatibility with existing functions
        self._load_legacy_globals()

        # Parse LDAP group mappings
        group_mappings = self._parse_group_mappings()

        # Get authentication token
        token = get_keycloak_token()

        # Execute setup steps
        success = True
        success &= self._setup_realm(token)
        success &= self._setup_ldap_federation(token)
        success &= self._setup_clients(token)
        success &= self._setup_roles_and_mappings(token, group_mappings)

        # Display final status
        self._display_status()

        return success

    def _load_legacy_globals(self) -> None:
        """Load legacy global variables for compatibility with existing functions."""
        global CUSTOMER_NAME, CUSTOMER_DOMAIN, BASE_DOMAIN, KC_HOSTNAME
        global KEYCLOAK_ADMIN_USERNAME, KEYCLOAK_ADMIN_PASSWORD, KEYCLOAK_REALM, VERIFY_SSL

        CUSTOMER_NAME = self.config['customer_name']
        CUSTOMER_DOMAIN = self.config['customer_domain']
        BASE_DOMAIN = self.config['base_domain']
        KC_HOSTNAME = self.config['kc_hostname']
        KEYCLOAK_ADMIN_USERNAME = self.config['admin_username']
        KEYCLOAK_ADMIN_PASSWORD = self.config['admin_password']
        KEYCLOAK_REALM = self.config['realm']
        VERIFY_SSL = self.verify_ssl

    def _parse_group_mappings(self) -> Dict[str, Dict[str, str]]:
        """Parse LDAP group mappings from environment variables."""
        group_mappings = {}
        group_mappings_str = os.getenv("LLDAP_GRUPPEN_KC_MAPPING", "")

        if group_mappings_str:
            # Format: "nextcloud:nc-users,nc-admins;leos360portal:portal-users,portal-admins"
            client_mappings = group_mappings_str.split(";")

            for mapping in client_mappings:
                if ":" in mapping:
                    client_name, groups = mapping.split(":", 1)
                    client_name = client_name.strip()

                    if "," in groups:
                        users_group, admins_group = [g.strip() for g in groups.split(",", 1)]
                        group_mappings[client_name] = {
                            "users": users_group,
                            "admins": admins_group
                        }
                        print_step(f"Group mapping for {client_name}: users='{users_group}', admins='{admins_group}'")

        return group_mappings

    def _setup_realm(self, token: str) -> bool:
        """Setup Keycloak realm."""
        print_step("Setting up Keycloak realm...")

        if check_realm_exists(token, self.config['realm']):
            self.status["realm"] = True
            print_success("Realm already exists")
        else:
            create_realm(token, self.config['realm'])
            if check_realm_exists(token, self.config['realm']):
                self.status["realm"] = True
                print_success("Realm created successfully")
            else:
                print_error("Failed to create realm")
                return False

        return True

    def _setup_ldap_federation(self, token: str) -> bool:
        """Setup LDAP federation and mappers."""
        print_step("Setting up LDAP federation...")

        federation_id = None

        if check_ldap_federation_exists(token, self.config['realm'], verbose=False):
            federation_id = get_ldap_federation_id(token, self.config['realm'])
            self.status["ldap"] = True
            print_success("LDAP federation already exists")
        else:
            federation_id = create_ldap_federation(
                token,
                self.config['realm'],
                self.config['base_dn'],
                self.config['customer_domain'],
                self.config['customer_ip'],
                self.config['ldap_ro_pass']
            )
            if federation_id:
                self.status["ldap"] = True
                print_success("LDAP federation created successfully")

        if federation_id:
            # Setup first name mapper
            self._setup_first_name_mapper(token, federation_id)

            # Setup group mapper
            self._setup_group_mapper(token, federation_id)

            # Synchronize LDAP groups
            self._sync_ldap_groups(token, federation_id)

        return federation_id is not None

    def _setup_first_name_mapper(self, token: str, federation_id: str) -> None:
        """Setup first name mapper for LDAP federation."""
        print_step("Setting up LDAP first name mapper...")

        if check_ldap_mapper_exists(token, self.config['realm'], federation_id, "first name", verbose=False):
            # Check if mapper already has correct LDAP attribute
            mapper_id = get_ldap_mapper_id(token, self.config['realm'], federation_id, "first name")
            mapper = get_ldap_mapper_by_id(token, self.config['realm'], mapper_id)

            if mapper and mapper.get('config', {}).get('ldap.attribute', [''])[0] == "givenName":
                print_success("First name mapper already correctly configured (ldap.attribute: givenName)")
                self.status["first_name_mapper"] = True
            else:
                update_ldap_mapper(
                    token=token,
                    realm_name=self.config['realm'],
                    federation_id=federation_id,
                    mapper_name="first name",
                    config_updates={"ldap.attribute": "givenName"}
                )
                self.status["first_name_mapper"] = True
                print_success("First name mapper updated successfully")
        else:
            print_warning("First name mapper does not exist - cannot update")

    def _setup_group_mapper(self, token: str, federation_id: str) -> None:
        """Setup group mapper for LDAP federation."""
        print_step("Setting up LDAP group mapper...")

        if check_ldap_mapper_exists(token, self.config['realm'], federation_id, "lldap-groups", verbose=False):
            print_success("LDAP group mapper already exists")
            self.status["group_mapper"] = True
        else:
            create_ldap_mapper_group(token, self.config['realm'], federation_id, self.config['base_dn'], self.config['customer_domain'])
            if check_ldap_mapper_exists(token, self.config['realm'], federation_id, "lldap-groups", verbose=False):
                self.status["group_mapper"] = True
                print_success("LDAP group mapper created successfully")

    def _sync_ldap_groups(self, token: str, federation_id: str) -> None:
        """Synchronize LDAP groups."""
        print_step("Synchronizing LDAP groups...")

        if self.status["group_mapper"] and federation_id:
            if sync_ldap_groups(token, self.config['realm'], federation_id):
                print_success("LDAP groups synchronized successfully!")
                time.sleep(5)  # Brief pause to allow synchronization to complete
            else:
                print_error("Error synchronizing LDAP groups!")

    def _setup_clients(self, token: str) -> bool:
        """Setup Keycloak clients."""
        print_step("Setting up Keycloak clients...")

        success = True

        # Setup Nextcloud client
        nextcloud_client_id = self._setup_nextcloud_client(token)
        if nextcloud_client_id:
            self.status["nextcloud"] = True
            print_success("Nextcloud client setup completed")
        else:
            success = False

        # Setup LEOS360 Portal client
        leos360portal_client_id = self._setup_portal_client(token)
        if leos360portal_client_id:
            self.status["leos360portal"] = True
            print_success("LEOS360 Portal client setup completed")
        else:
            success = False

        return success

    def _setup_nextcloud_client(self, token: str) -> Optional[str]:
        """Setup Nextcloud client."""
        print_step("Setting up Nextcloud client...")

        if check_client_exists(token, self.config['realm'], "nextcloud", verbose=False):
            print_success("Nextcloud client already exists")
            # Get existing client ID
            clients = get_realm_clients(token, self.config['realm'])
            for client in clients:
                if client.get('clientId') == "nextcloud":
                    return client.get('id')
        else:
            client_id = create_client_nextcloud(token, self.config['realm'], self.config['customer_domain'])
            if client_id:
                print_success("Nextcloud client created successfully")
                return client_id
            else:
                print_error("Failed to create Nextcloud client")

        return None

    def _setup_portal_client(self, token: str) -> Optional[str]:
        """Setup LEOS360 Portal client."""
        print_step("Setting up LEOS360 Portal client...")

        if check_client_exists(token, self.config['realm'], "leos360portal", verbose=False):
            print_success("LEOS360 Portal client already exists")
            # Get existing client ID
            clients = get_realm_clients(token, self.config['realm'])
            for client in clients:
                if client.get('clientId') == "leos360portal":
                    return client.get('id')
        else:
            client_id = create_client_portal(token, self.config['realm'], self.config['customer_domain'])
            if client_id:
                print_success("LEOS360 Portal client created successfully")
                return client_id
            else:
                print_error("Failed to create LEOS360 Portal client")

        return None

    def _setup_roles_and_mappings(self, token: str, group_mappings: Dict[str, Dict[str, str]]) -> bool:
        """Setup client roles and group mappings."""
        print_step("Setting up client roles and group mappings...")

        success = True

        # Setup Nextcloud roles and mappings
        if self.status["nextcloud"]:
            nextcloud_client_id = self._get_client_id(token, "nextcloud")
            if nextcloud_client_id:
                nc_mapping = group_mappings.get("nextcloud", {})
                nc_users_group = nc_mapping.get("users")
                nc_admins_group = nc_mapping.get("admins")

                setup_client_roles_and_mappings(
                    token,
                    self.config['realm'],
                    nextcloud_client_id,
                    "nextcloud",
                    self.status,
                    user_group=nc_users_group,
                    admin_group=nc_admins_group
                )

        # Setup LEOS360 Portal roles and mappings
        if self.status["leos360portal"]:
            portal_client_id = self._get_client_id(token, "leos360portal")
            if portal_client_id:
                portal_mapping = group_mappings.get("leos360portal", {})
                portal_users_group = portal_mapping.get("users")
                portal_admins_group = portal_mapping.get("admins")

                setup_client_roles_and_mappings(
                    token,
                    self.config['realm'],
                    portal_client_id,
                    "leos360portal",
                    self.status,
                    user_group=portal_users_group,
                    admin_group=portal_admins_group
                )

        return success

    def _get_client_id(self, token: str, client_name: str) -> Optional[str]:
        """Get client ID by client name."""
        clients = get_realm_clients(token, self.config['realm'])
        for client in clients:
            if client.get('clientId') == client_name:
                return client.get('id')
        return None

    def _display_status(self) -> None:
        """Display final status of the setup."""
        print_header("SETUP STATUS")

        status_items = [
            ("Realm", self.status['realm']),
            ("LDAP User Federation", self.status['ldap']),
            ("First Name Mapper", self.status['first_name_mapper']),
            ("LDAP Group Mapper", self.status['group_mapper']),
            ("Nextcloud Client", self.status['nextcloud']),
            ("LEOS360 Portal Client", self.status['leos360portal']),
            ("Nextcloud Roles Created", self.status['nextcloud_roles_created']),
            ("Nextcloud Role Mapping", self.status['nextcloud_role_mapping']),
            ("LEOS360 Portal Roles Created", self.status['leos360portal_roles_created']),
            ("LEOS360 Portal Role Mapping", self.status['leos360portal_role_mapping'])
        ]

        for item_name, item_status in status_items:
            status_symbol = '✓' if item_status else '✗'
            print(f"  {item_name}: {status_symbol}")

    def show_status(self) -> bool:
        """Show current status of Keycloak setup."""
        print_step("Checking Keycloak setup status...")
        # Implementation for status checking would go here
        self._display_status()
        return True


# =============================================================================
# ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main entry point for the script.

    Handles command line argument parsing and script execution.
    """
    parser = argparse.ArgumentParser(
        description=f"""
{SCRIPT_NAME} v{SCRIPT_VERSION}

This script handles the Keycloak setup for a new tenant in the LEOS360 platform.
It creates realms, clients, LDAP federation, and configures authentication flows
for the complete identity management infrastructure.

Requirements:
  - Stage 1, 2, and 3 must be completed successfully
  - Keycloak server must be accessible
  - LLDAP container must be running and configured
  - Environment variables must be properly configured
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match previous stages)'
    )
    parser.add_argument(
        '--status',
        action='store_true',
        help='Show status of the Keycloak setup'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    operation = "status" if args.status else "setup"

    try:
        # Create and run Keycloak setup
        setup = TenantKeycloakSetup(args.customer_name)
        success = setup.run(operation)

        if not success:
            sys.exit(1)

    except KeyboardInterrupt:
        print_error("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()