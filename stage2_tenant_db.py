#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 2: Database Setup
==========================================

This script handles the database setup for a new tenant in the LEOS360 platform.
It creates PostgreSQL databases, users, and permissions for all required services
including Nextcloud, Keycloak, and LLDAP.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 must be completed successfully
- PostgreSQL server must be accessible at ************
- Database admin credentials must be available
- SQL setup file must exist in tenant directory

Usage:
    python3 stage2_tenant_db.py <customer_name> [options]

Examples:
    python3 stage2_tenant_db.py example-customer
    python3 stage2_tenant_db.py example-customer --status
    python3 stage2_tenant_db.py example-customer --reset
"""

import os
import sys
import subprocess
import tempfile
import argparse
from pathlib import Path
from typing import Optional
import psycopg2
import dotenv

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 2: Database Setup"

# Database connection details
DB_HOST = "************"
DB_PORT = 5432
DB_ADMIN_USER = "postgres"
DB_TEMPLATE = "template1"

# File paths and patterns
BASE_TENANT_PATH = Path("/mnt/storage/tenants")
SQL_FILE_PATTERN = "{base_path}/{customer_name}/db/db_setup_{customer_name}.sql"
ENV_FILE_PATTERN = "{base_path}/{customer_name}/.env"

# Database and user naming patterns
DB_NEXTCLOUD = "{customer_name}_nextcloud"
DB_KEYCLOAK = "{customer_name}_keycloak"
DB_LLDAP = "{customer_name}_lldap"
DB_USER = "{customer_name}_admin"

# Environment variable names
ENV_VAR_DB_PASSWORD = "DB_ADMIN_PASSWORD"


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


# =============================================================================
# MAIN DATABASE SETUP CLASS
# =============================================================================

class TenantDatabaseSetup:
    """
    Handles database setup for a new tenant in the LEOS360 platform.

    This class manages the complete database setup process including:
    - Database creation for Nextcloud, Keycloak, and LLDAP
    - User creation and permission assignment
    - Status checking and reset operations
    - PostgreSQL connection management
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant database setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValueError: If customer_name is invalid
            FileNotFoundError: If required files are missing
        """
        self.customer_name = customer_name
        print_step(f"Initializing database setup for: {customer_name}")

        # Database names
        self.db_nextcloud = DB_NEXTCLOUD.format(customer_name=customer_name)
        self.db_keycloak = DB_KEYCLOAK.format(customer_name=customer_name)
        self.db_lldap = DB_LLDAP.format(customer_name=customer_name)
        self.db_user = DB_USER.format(customer_name=customer_name)

        # File paths
        self.env_file = ENV_FILE_PATTERN.format(
            base_path=BASE_TENANT_PATH,
            customer_name=customer_name
        )
        self.sql_file = SQL_FILE_PATTERN.format(
            base_path=BASE_TENANT_PATH,
            customer_name=customer_name
        )

        # Runtime variables
        self.db_admin_password = ""
        self.pgpass_filename = ""

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def read_env_file(self) -> None:
        """
        Read environment file and extract database admin password.

        Raises:
            FileNotFoundError: If environment file doesn't exist
            ValueError: If required environment variables are missing
        """
        if not os.path.isfile(self.env_file):
            raise FileNotFoundError(f"Environment file does not exist: {self.env_file}")

        dotenv.load_dotenv(self.env_file)
        self.db_admin_password = os.getenv(ENV_VAR_DB_PASSWORD)
        if not self.db_admin_password:
            raise ValueError(f"{ENV_VAR_DB_PASSWORD} not found in .env file")

        print_step("Environment file loaded successfully")

    def create_pgpass_file(self) -> None:
        """
        Create temporary .pgpass file for PostgreSQL authentication.
        """
        pgpass_file = tempfile.NamedTemporaryFile(delete=False, mode='w', encoding='utf-8')
        pgpass_file.write(f"{DB_HOST}:{DB_PORT}:*:{DB_ADMIN_USER}:{self.db_admin_password}\n")
        pgpass_file.close()
        self.pgpass_filename = pgpass_file.name
        os.environ["PGPASSFILE"] = self.pgpass_filename
        print_step("PostgreSQL authentication file created")

    def cleanup_pgpass_file(self) -> None:
        """
        Clean up temporary .pgpass file.
        """
        if self.pgpass_filename and os.path.exists(self.pgpass_filename):
            os.remove(self.pgpass_filename)
            print_step("Temporary authentication file cleaned up")

    def run_psql_command(self, command: list, error_message: str) -> subprocess.CompletedProcess:
        """
        Execute a psql command with error handling.

        Args:
            command: List of command arguments
            error_message: Error message to display on failure

        Returns:
            CompletedProcess result

        Raises:
            Exception: If command execution fails
        """
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode != 0:
            print_error(f"{error_message}")
            print_error(f"Command error: {result.stderr}")
            raise Exception(error_message)
        return result

    # =========================================================================
    # DATABASE QUERY METHODS
    # =========================================================================

    def get_db_count(self, cursor) -> int:
        """
        Get count of existing databases for this customer.

        Args:
            cursor: Database cursor

        Returns:
            Number of existing databases
        """
        cursor.execute(
            """
            SELECT COUNT(*) FROM pg_database
            WHERE datname IN (%s, %s, %s);
            """,
            (self.db_nextcloud, self.db_keycloak, self.db_lldap),
        )
        return cursor.fetchone()[0]

    def get_user_count(self, cursor) -> int:
        """
        Get count of existing database users for this customer.

        Args:
            cursor: Database cursor

        Returns:
            Number of existing users (0 or 1)
        """
        cursor.execute(
            """
            SELECT COUNT(*) FROM pg_roles
            WHERE rolname = %s;
            """,
            (self.db_user,),
        )
        return cursor.fetchone()[0]

    def get_existing_databases(self, cursor) -> list:
        """
        Get list of existing databases for this customer.

        Args:
            cursor: Database cursor

        Returns:
            List of existing database names
        """
        cursor.execute(
            """
            SELECT datname FROM pg_database
            WHERE datname IN (%s, %s, %s);
            """,
            (self.db_nextcloud, self.db_keycloak, self.db_lldap),
        )
        return [row[0] for row in cursor.fetchall()]

    # =========================================================================
    # DATABASE OPERATION METHODS
    # =========================================================================

    def show_status(self) -> None:
        """
        Show status of databases and user for this customer.
        """
        print_step(f"Checking status for customer: {self.customer_name}")

        with psycopg2.connect(
            user=DB_ADMIN_USER,
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_TEMPLATE,
            password=self.db_admin_password
        ) as conn:
            with conn.cursor() as cur:
                db_count = self.get_db_count(cur)
                user_exists = self.get_user_count(cur)

                print(f"Status for customer {self.customer_name}:")
                print(f"- User {self.db_user} {'exists' if user_exists else 'does not exist'}")

                if db_count > 0:
                    print("- Databases:")
                    existing_dbs = self.get_existing_databases(cur)
                    for db_name in existing_dbs:
                        print(f"  - {db_name}")
                else:
                    print("- No databases found")

        print_success("Status check completed")

    def reset_databases(self) -> None:
        """
        Reset (delete) all databases and user for this customer.
        """
        print_step(f"Resetting databases for customer: {self.customer_name}")

        with psycopg2.connect(
            user=DB_ADMIN_USER,
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_TEMPLATE,
            password=self.db_admin_password
        ) as conn:
            with conn.cursor() as cur:
                db_count = self.get_db_count(cur)
                user_exists = self.get_user_count(cur)

                if db_count == 0 and user_exists == 0:
                    print_warning("No objects to delete")
                    return

                print("Following objects will be deleted:")
                if user_exists:
                    print(f"- User {self.db_user}")
                if db_count > 0:
                    print("- Databases:")
                    existing_dbs = self.get_existing_databases(cur)
                    for db_name in existing_dbs:
                        print(f"  - {db_name}")

                # Create temporary SQL file for reset operations
                reset_sql = tempfile.NamedTemporaryFile(delete=False, mode='w', encoding='utf-8')
                reset_sql_name = reset_sql.name

                try:
                    with open(reset_sql_name, 'w', encoding='utf-8') as sql_file:
                        sql_file.write(f"""
                            -- Terminate connections
                            SELECT pg_terminate_backend(pid)
                            FROM pg_stat_activity
                            WHERE datname IN ('{self.db_nextcloud}', '{self.db_keycloak}', '{self.db_lldap}');

                            -- Drop databases
                            DROP DATABASE IF EXISTS {self.db_nextcloud};
                            DROP DATABASE IF EXISTS {self.db_keycloak};
                            DROP DATABASE IF EXISTS {self.db_lldap};

                            -- Drop user (this must come after dropping the databases)
                            DROP USER IF EXISTS {self.db_user};
                        """)

                    psql_command = [
                        "psql",
                        "-U", DB_ADMIN_USER,
                        "-h", DB_HOST,
                        "-p", str(DB_PORT),
                        "-d", DB_TEMPLATE,
                        "-f", reset_sql_name,
                        "--no-psqlrc"
                    ]
                    self.run_psql_command(psql_command, "Failed to reset database")
                    print_success("Database reset completed successfully")
                finally:
                    os.remove(reset_sql_name)

    def setup_databases(self) -> None:
        """
        Set up databases and user for this customer.
        """
        print_step(f"Setting up databases for customer: {self.customer_name}")

        # Check if SQL file exists
        if not os.path.isfile(self.sql_file):
            raise FileNotFoundError(f"SQL file does not exist: {self.sql_file}")

        with psycopg2.connect(
            user=DB_ADMIN_USER,
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_TEMPLATE,
            password=self.db_admin_password
        ) as conn:
            with conn.cursor() as cur:
                db_count = self.get_db_count(cur)
                user_exists = self.get_user_count(cur)

                # Check if objects already exist
                if db_count > 0 or user_exists > 0:
                    print_error(f"Setup cannot be performed because objects already exist for {self.customer_name}:")
                    if user_exists:
                        print_error(f"- User {self.db_user} already exists")
                    if db_count > 0:
                        print_error("- Databases already exist:")
                        existing_dbs = self.get_existing_databases(cur)
                        for db_name in existing_dbs:
                            print_error(f"  - {db_name}")
                    print_error("Use --reset first to delete existing objects and then run the setup again")
                    return

                # Execute SQL setup file
                psql_command = [
                    "psql",
                    "-U", DB_ADMIN_USER,
                    "-h", DB_HOST,
                    "-p", str(DB_PORT),
                    "-d", DB_TEMPLATE,
                    "-f", self.sql_file,
                    "--no-psqlrc"
                ]
                self.run_psql_command(psql_command, "Failed to setup database")
                print_success("Database setup completed successfully")

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str) -> bool:
        """
        Main execution function that orchestrates the database setup process.

        Args:
            operation: Operation to perform ('setup', 'status', or 'reset')

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 Database Setup - {self.customer_name}")
        print_step(f"Starting database {operation} for {self.customer_name}...")

        try:
            # Step 1: Read environment file
            self.read_env_file()

            # Step 2: Create PostgreSQL authentication file
            self.create_pgpass_file()

            # Step 3: Perform requested operation
            if operation == "status":
                self.show_status()
            elif operation == "reset":
                self.reset_databases()
            else:  # default: setup
                self.setup_databases()

            print_header("OPERATION COMPLETED SUCCESSFULLY")
            print_success(f"Database {operation} for {self.customer_name} completed successfully!")

            return True

        except Exception as e:
            print_error(f"Database {operation} failed: {str(e)}")
            return False

        finally:
            # Always clean up temporary files
            self.cleanup_pgpass_file()


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute database setup.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Setup tenant database for LEOS360 platform',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage2_tenant_db.py example-customer
  python3 stage2_tenant_db.py example-customer --status
  python3 stage2_tenant_db.py example-customer --reset

Requirements:
  - Stage 1 must be completed successfully
  - PostgreSQL server must be accessible at ************
  - Database admin credentials must be available
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    parser.add_argument(
        '--status',
        action='store_true',
        help='Show status of databases and user'
    )
    parser.add_argument(
        '--reset',
        action='store_true',
        help='Reset (delete) existing databases and user'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    if args.status:
        operation = "status"
    elif args.reset:
        operation = "reset"
    else:
        operation = "setup"

    try:
        # Create and run database setup
        setup = TenantDatabaseSetup(args.customer_name)
        success = setup.run(operation)

        if not success:
            sys.exit(1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except FileNotFoundError as e:
        print_error(f"File not found: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Database setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()